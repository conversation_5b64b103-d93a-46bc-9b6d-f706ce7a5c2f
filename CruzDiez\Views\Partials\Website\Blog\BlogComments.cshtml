﻿@inherits UmbracoViewPage<SiteBuilderBaseViewModel>
@using USNSiteBuilder.Core.Models
@using USNSiteBuilder.Core.Extensions
@{
    int blogLandingPageID = Convert.ToInt32(ViewData["blogLandingPageID"]);
    UsnblogLandingPage landing = (UsnblogLandingPage)Umbraco.Content(blogLandingPageID);

    if (landing.DisqusUsername.HasValue() && !Model.Value<bool>("disableComments"))
    {
        <section id="comments" class="content component comments base-bg">
            <div id="disqus_thread"></div>
            <script type="text/javascript">
            /* * * CONFIGURATION VARIABLES: EDIT BEFORE PASTING INTO YOUR WEBPAGE * * */
            var disqus_shortname = '@landing.DisqusUsername'; // required: replace example with your forum shortname

            /* * * DON'T EDIT BELOW THIS LINE * * */
            (function () {
                var dsq = document.createElement('script'); dsq.type = 'text/javascript'; dsq.async = true;
                dsq.src = '//' + disqus_shortname + '.disqus.com/embed.js';
                (document.getElementsByTagName('head')[0] || document.getElementsByTagName('body')[0]).appendChild(dsq);
            })();
            </script>
            <noscript>Please enable JavaScript to view the <a href="http://disqus.com/?ref_noscript">comments powered by Disqus.</a></noscript>
            <a href="https://disqus.com" class="dsq-brlink">comments powered by <span class="logo-disqus">Disqus</span></a>
        </section>
    }
}
