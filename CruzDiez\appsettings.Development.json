{
  "$schema": "./umbraco/config/appsettings-schema.json",
  "Serilog": {
    "MinimumLevel": {
      "Default": "Warning",
      "Override": {
        "Microsoft": "Warning",
        "Microsoft.Hosting.Lifetime": "Information",
        "System": "Warning",
        "Umbraco": "Warning"
        //"Microsoft.AspNetCore.Mvc": "Verbose"
      }
    },
    "WriteTo": [
      {
        "Name": "Async",
        "Args": {
          "configure": [
            {
              "Name": "Console"
            }
          ]
        }
      }
    ]
  },
  "Umbraco": {
    "CMS": {
      "Content": {
        "MacroErrors": "Inline" //"Throw"
      },
      "Global": {
        "Smtp": {
          "From": "<EMAIL>",
          "Host": "smtp.gmail.com",
          "Port": 587,
          "SecureSocketOptions": "StartTls",
          "Username": "<EMAIL>",
          "Password": "@W3b51t3_CD#"
        }
      },
      "UseHttps": true,
      "Hosting": {
        "Debug": true
      },
      "RuntimeMinification": {
        "useInMemoryCache": true,
        "cacheBuster": "Timestamp"
      }
    }
  }
}
