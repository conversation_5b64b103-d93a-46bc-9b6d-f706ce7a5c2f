﻿<?xml version="1.0" encoding="utf-8"?>
<DataType Key="d6b8f6cd-ca3c-4efc-983d-94a3a59c5578" Alias="List View - USNStylesFolder" Level="3">
  <Info>
    <Name>List View - USNStylesFolder</Name>
    <EditorAlias>Umbraco.ListView</EditorAlias>
    <DatabaseType>Nvarchar</DatabaseType>
    <Folder>USN+Data+Types/List+View</Folder>
  </Info>
  <Config><![CDATA[{
  "PageSize": 21,
  "OrderBy": "sortOrder",
  "OrderDirection": "asc",
  "IncludeProperties": [
    {
      "alias": "updateDate",
      "header": "Last edited",
      "nameTemplate": null,
      "isSystem": 1
    },
    {
      "alias": "updater",
      "header": null,
      "nameTemplate": null,
      "isSystem": 1
    },
    {
      "alias": "owner",
      "header": "Created by",
      "nameTemplate": null,
      "isSystem": 1
    },
    {
      "alias": "sortOrder",
      "header": null,
      "nameTemplate": null,
      "isSystem": 1
    },
    {
      "alias": "styleColors",
      "header": "Colors",
      "nameTemplate": "",
      "isSystem": 0
    }
  ],
  "Layouts": [
    {
      "name": "Design",
      "path": "/App_Plugins/uSkinned/views/designs-listview.html",
      "icon": "icon-brush color-black",
      "isSystem": 0,
      "selected": true
    },
    {
      "name": "grid",
      "path": "views/propertyeditors/listview/layouts/grid/grid.html",
      "icon": "icon-thumbnails-small",
      "isSystem": 1,
      "selected": false
    },
    {
      "name": "List",
      "path": "views/propertyeditors/listview/layouts/list/list.html",
      "icon": "icon-list",
      "isSystem": 1,
      "selected": false
    }
  ],
  "BulkActionPermissions": {
    "allowBulkPublish": true,
    "allowBulkUnpublish": true,
    "allowBulkCopy": true,
    "allowBulkMove": true,
    "allowBulkDelete": true
  },
  "Icon": "icon-color-bucket color-purple",
  "TabName": "Designs",
  "ShowContentFirst": false,
  "UseInfiniteEditor": false
}]]></Config>
</DataType>