{"@t":"2023-02-09T08:27:45.8548027Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":24976,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:27:45.8660117Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":24976,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:27:45.8707108Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":24976,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:27:45.8707704Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":24976,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:27:45.8707871Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":24976,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:27:59.9586590Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAEU2O7C3J:00000033","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMOAEU2O7C3J","ProcessId":24976,"ProcessName":"CruzDiez","ThreadId":3,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T08:28:34.0777745Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":11360,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:28:34.0891506Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":11360,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:28:34.0942141Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":11360,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:28:34.0942894Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":11360,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:28:34.0943046Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":11360,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:28:47.7122053Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAEUH6L74P:00000025","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMOAEUH6L74P","ProcessId":11360,"ProcessName":"CruzDiez","ThreadId":3,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T08:29:12.7770920Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidCastException: Unable to cast object of type 'Umbraco.Cms.Web.Common.PublishedModels.UsnglobalSettings' to type 'CruzDiez.Models.ReCaptchaConfiguration.IReCaptchaConfiguration'.\r\n   at AspNetCore.Views_Partials_Website_Forms_ContactForm.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\Forms\\ContactForm.cshtml:line 11\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.ViewViewComponentResult.ExecuteAsync(ViewComponentContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.DefaultViewComponentInvoker.InvokeAsync(ViewComponentContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.DefaultViewComponentHelper.InvokeCoreAsync(ViewComponentDescriptor descriptor, Object arguments)\r\n   at AspNetCore.Views_Partials_Website_Blocks_Form.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\Blocks\\Form.cshtml:line 38\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_Partials_Website_BlockContainers_ComponentSection.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\BlockContainers\\ComponentSection.cshtml:line 71\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_Partials_Website_BlockContainers_BlockComponents.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\BlockContainers\\BlockComponents.cshtml:line 18\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_USNPage.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\USNPage.cshtml:line 19\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)\r\n   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)\r\n   at Umbraco.Cms.Web.Common.Middleware.BasicAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.BackOffice.Middleware.BackOfficeExternalLoginProviderErrorMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAEUH6L74P:00000027","RequestPath":"/contact","ConnectionId":"0HMOAEUH6L74P","ProcessId":11360,"ProcessName":"CruzDiez","ThreadId":3,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T08:34:31.7679929Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":24376,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:34:31.7789893Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":24376,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:34:31.7847357Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":24376,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:34:31.7848359Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":24376,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:34:31.7848539Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":24376,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:34:36.8617555Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.CompilationFailedException: One or more compilation failures occurred:\r\nC:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\USNPage.cshtml(5,5): error CS0246: The type or namespace name 'Usnstyle' could not be found (are you missing a using directive or an assembly reference?)\r\nC:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\USNPage.cshtml(5,30): error CS0246: The type or namespace name 'Usnstyle' could not be found (are you missing a using directive or an assembly reference?)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.RuntimeViewCompiler.CompileAndEmit(RazorCodeDocument codeDocument, String generatedCode)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.RuntimeViewCompiler.CompileAndEmit(String relativePath)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.RuntimeViewCompiler.OnCacheMiss(String normalizedPath)\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Razor.Compilation.DefaultRazorPageFactoryProvider.CreateFactory(String relativePath)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorViewEngine.CreateCacheResult(HashSet`1 expirationTokens, String relativePath, Boolean isMainPage)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorViewEngine.OnCacheMiss(ViewLocationExpanderContext expanderContext, ViewLocationCacheKey cacheKey)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorViewEngine.LocatePageFromViewLocations(ActionContext actionContext, String pageName, Boolean isMainPage)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorViewEngine.FindView(ActionContext context, String viewName, Boolean isMainPage)\r\n   at Umbraco.Cms.Web.Common.ModelsBuilder.RefreshingRazorViewEngine.FindView(ActionContext context, String viewName, Boolean isMainPage)\r\n   at Umbraco.Cms.Web.Website.ViewEngines.ProfilingViewEngine.FindView(ActionContext context, String viewName, Boolean isMainPage)\r\n   at Microsoft.AspNetCore.Mvc.ViewEngines.CompositeViewEngine.FindView(ActionContext context, String viewName, Boolean isMainPage)\r\n   at Umbraco.Cms.Web.Common.Controllers.UmbracoPageController.EnsurePhsyicalViewExists(String template)\r\n   at Umbraco.Cms.Web.Common.Controllers.RenderController.CurrentTemplate[T](T model)\r\n   at USNSiteBuilder.Core.Controllers.Frontend.BaseController.Index()\r\n   at lambda_method105(Closure , Object , Object[] )\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ExceptionContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeNextResourceFilter()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)\r\n   at Umbraco.Cms.Web.Common.Middleware.BasicAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.BackOffice.Middleware.BackOfficeExternalLoginProviderErrorMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAF1RLOA3T:0000000F","RequestPath":"/","ConnectionId":"0HMOAF1RLOA3T","ProcessId":24376,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T08:35:09.3196622Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":24948,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:35:09.3319870Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":24948,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:35:09.3379240Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":24948,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:35:09.3379900Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":24948,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:35:09.3380090Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":24948,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:35:14.6477652Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.CompilationFailedException: One or more compilation failures occurred:\r\nC:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\USNPage.cshtml(5,5): error CS0246: The type or namespace name 'Usnstyle' could not be found (are you missing a using directive or an assembly reference?)\r\nC:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\USNPage.cshtml(5,30): error CS0246: The type or namespace name 'Usnstyle' could not be found (are you missing a using directive or an assembly reference?)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.RuntimeViewCompiler.CompileAndEmit(RazorCodeDocument codeDocument, String generatedCode)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.RuntimeViewCompiler.CompileAndEmit(String relativePath)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.RuntimeViewCompiler.OnCacheMiss(String normalizedPath)\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Razor.Compilation.DefaultRazorPageFactoryProvider.CreateFactory(String relativePath)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorViewEngine.CreateCacheResult(HashSet`1 expirationTokens, String relativePath, Boolean isMainPage)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorViewEngine.OnCacheMiss(ViewLocationExpanderContext expanderContext, ViewLocationCacheKey cacheKey)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorViewEngine.LocatePageFromViewLocations(ActionContext actionContext, String pageName, Boolean isMainPage)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorViewEngine.FindView(ActionContext context, String viewName, Boolean isMainPage)\r\n   at Umbraco.Cms.Web.Common.ModelsBuilder.RefreshingRazorViewEngine.FindView(ActionContext context, String viewName, Boolean isMainPage)\r\n   at Umbraco.Cms.Web.Website.ViewEngines.ProfilingViewEngine.FindView(ActionContext context, String viewName, Boolean isMainPage)\r\n   at Microsoft.AspNetCore.Mvc.ViewEngines.CompositeViewEngine.FindView(ActionContext context, String viewName, Boolean isMainPage)\r\n   at Umbraco.Cms.Web.Common.Controllers.UmbracoPageController.EnsurePhsyicalViewExists(String template)\r\n   at Umbraco.Cms.Web.Common.Controllers.RenderController.CurrentTemplate[T](T model)\r\n   at USNSiteBuilder.Core.Controllers.Frontend.BaseController.Index()\r\n   at lambda_method105(Closure , Object , Object[] )\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ExceptionContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeNextResourceFilter()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)\r\n   at Umbraco.Cms.Web.Common.Middleware.BasicAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.BackOffice.Middleware.BackOfficeExternalLoginProviderErrorMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAF26U62J2:0000000F","RequestPath":"/","ConnectionId":"0HMOAF26U62J2","ProcessId":24948,"ProcessName":"CruzDiez","ThreadId":3,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T08:36:41.5436887Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":19932,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:36:41.5549514Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":19932,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:36:41.5596544Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":19932,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:36:41.5597149Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":19932,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:36:41.5597276Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":19932,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:36:55.0852533Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAF32DONHR:00000029","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMOAF32DONHR","ProcessId":19932,"ProcessName":"CruzDiez","ThreadId":3,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T08:45:49.0469133Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":28452,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:45:49.0576690Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":28452,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:45:49.0627502Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":28452,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:45:49.0628271Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":28452,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:45:49.0628404Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":28452,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:46:03.0986624Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAF85K7L8V:00000027","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMOAF85K7L8V","ProcessId":28452,"ProcessName":"CruzDiez","ThreadId":3,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T08:46:18.4621127Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidCastException: Unable to cast object of type 'Umbraco.Cms.Web.Common.PublishedModels.UsnglobalSettings' to type 'USNSiteBuilder.Core.Models.UmbracoFormsAppSettings'.\r\n   at AspNetCore.Views_Partials_Website_Forms_ContactForm.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\Forms\\ContactForm.cshtml:line 11\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.ViewViewComponentResult.ExecuteAsync(ViewComponentContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.DefaultViewComponentInvoker.InvokeAsync(ViewComponentContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.DefaultViewComponentHelper.InvokeCoreAsync(ViewComponentDescriptor descriptor, Object arguments)\r\n   at AspNetCore.Views_Partials_Website_Blocks_Form.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\Blocks\\Form.cshtml:line 38\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_Partials_Website_BlockContainers_ComponentSection.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\BlockContainers\\ComponentSection.cshtml:line 71\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_Partials_Website_BlockContainers_BlockComponents.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\BlockContainers\\BlockComponents.cshtml:line 18\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_USNPage.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\USNPage.cshtml:line 19\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)\r\n   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)\r\n   at Umbraco.Cms.Web.Common.Middleware.BasicAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.BackOffice.Middleware.BackOfficeExternalLoginProviderErrorMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAF85K7L8V:0000002B","RequestPath":"/contact","ConnectionId":"0HMOAF85K7L8V","ProcessId":28452,"ProcessName":"CruzDiez","ThreadId":3,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T08:49:50.8347868Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":25776,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:49:50.8455629Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":25776,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:49:50.8510745Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":25776,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:49:50.8511514Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":25776,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:49:50.8511726Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":25776,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:49:56.2323004Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.CompilationFailedException: One or more compilation failures occurred:\r\nC:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\USNPage.cshtml(5,5): error CS0246: The type or namespace name 'Usnstyle' could not be found (are you missing a using directive or an assembly reference?)\r\nC:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\USNPage.cshtml(5,30): error CS0246: The type or namespace name 'Usnstyle' could not be found (are you missing a using directive or an assembly reference?)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.RuntimeViewCompiler.CompileAndEmit(RazorCodeDocument codeDocument, String generatedCode)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.RuntimeViewCompiler.CompileAndEmit(String relativePath)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.RuntimeViewCompiler.OnCacheMiss(String normalizedPath)\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Razor.Compilation.DefaultRazorPageFactoryProvider.CreateFactory(String relativePath)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorViewEngine.CreateCacheResult(HashSet`1 expirationTokens, String relativePath, Boolean isMainPage)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorViewEngine.OnCacheMiss(ViewLocationExpanderContext expanderContext, ViewLocationCacheKey cacheKey)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorViewEngine.LocatePageFromViewLocations(ActionContext actionContext, String pageName, Boolean isMainPage)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorViewEngine.FindView(ActionContext context, String viewName, Boolean isMainPage)\r\n   at Umbraco.Cms.Web.Common.ModelsBuilder.RefreshingRazorViewEngine.FindView(ActionContext context, String viewName, Boolean isMainPage)\r\n   at Umbraco.Cms.Web.Website.ViewEngines.ProfilingViewEngine.FindView(ActionContext context, String viewName, Boolean isMainPage)\r\n   at Microsoft.AspNetCore.Mvc.ViewEngines.CompositeViewEngine.FindView(ActionContext context, String viewName, Boolean isMainPage)\r\n   at Umbraco.Cms.Web.Common.Controllers.UmbracoPageController.EnsurePhsyicalViewExists(String template)\r\n   at Umbraco.Cms.Web.Common.Controllers.RenderController.CurrentTemplate[T](T model)\r\n   at USNSiteBuilder.Core.Controllers.Frontend.BaseController.Index()\r\n   at lambda_method105(Closure , Object , Object[] )\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ExceptionContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeNextResourceFilter()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)\r\n   at Umbraco.Cms.Web.Common.Middleware.BasicAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.BackOffice.Middleware.BackOfficeExternalLoginProviderErrorMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAFADLT8ED:0000000F","RequestPath":"/","ConnectionId":"0HMOAFADLT8ED","ProcessId":25776,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T08:55:14.1208189Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":29112,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:55:14.1320126Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":29112,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:55:14.1368840Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":29112,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:55:14.1369424Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":29112,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:55:14.1369566Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":29112,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:55:19.4433949Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.CompilationFailedException: One or more compilation failures occurred:\r\nC:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\USNPage.cshtml(5,5): error CS0246: The type or namespace name 'Usnstyle' could not be found (are you missing a using directive or an assembly reference?)\r\nC:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\USNPage.cshtml(5,30): error CS0246: The type or namespace name 'Usnstyle' could not be found (are you missing a using directive or an assembly reference?)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.RuntimeViewCompiler.CompileAndEmit(RazorCodeDocument codeDocument, String generatedCode)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.RuntimeViewCompiler.CompileAndEmit(String relativePath)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.RuntimeViewCompiler.OnCacheMiss(String normalizedPath)\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Razor.Compilation.DefaultRazorPageFactoryProvider.CreateFactory(String relativePath)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorViewEngine.CreateCacheResult(HashSet`1 expirationTokens, String relativePath, Boolean isMainPage)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorViewEngine.OnCacheMiss(ViewLocationExpanderContext expanderContext, ViewLocationCacheKey cacheKey)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorViewEngine.LocatePageFromViewLocations(ActionContext actionContext, String pageName, Boolean isMainPage)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorViewEngine.FindView(ActionContext context, String viewName, Boolean isMainPage)\r\n   at Umbraco.Cms.Web.Common.ModelsBuilder.RefreshingRazorViewEngine.FindView(ActionContext context, String viewName, Boolean isMainPage)\r\n   at Umbraco.Cms.Web.Website.ViewEngines.ProfilingViewEngine.FindView(ActionContext context, String viewName, Boolean isMainPage)\r\n   at Microsoft.AspNetCore.Mvc.ViewEngines.CompositeViewEngine.FindView(ActionContext context, String viewName, Boolean isMainPage)\r\n   at Umbraco.Cms.Web.Common.Controllers.UmbracoPageController.EnsurePhsyicalViewExists(String template)\r\n   at Umbraco.Cms.Web.Common.Controllers.RenderController.CurrentTemplate[T](T model)\r\n   at USNSiteBuilder.Core.Controllers.Frontend.BaseController.Index()\r\n   at lambda_method105(Closure , Object , Object[] )\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ExceptionContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeNextResourceFilter()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)\r\n   at Umbraco.Cms.Web.Common.Middleware.BasicAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.BackOffice.Middleware.BackOfficeExternalLoginProviderErrorMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAFDE1CST7:0000000F","RequestPath":"/","ConnectionId":"0HMOAFDE1CST7","ProcessId":29112,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T08:56:01.5690245Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":7172,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:56:01.5811737Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":7172,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:56:01.5877021Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":7172,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:56:01.5877876Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":7172,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:56:01.5878020Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":7172,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:56:14.9846735Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAFDS2ORF7:00000029","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMOAFDS2ORF7","ProcessId":7172,"ProcessName":"CruzDiez","ThreadId":3,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T08:56:31.2998427Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidCastException: Unable to cast object of type 'Umbraco.Cms.Web.Common.PublishedModels.UsnglobalSettings' to type 'USNSiteBuilder.Core.Models.UmbracoFormsAppSettings'.\r\n   at AspNetCore.Views_Partials_Website_Forms_ContactForm.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\Forms\\ContactForm.cshtml:line 11\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.ViewViewComponentResult.ExecuteAsync(ViewComponentContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.DefaultViewComponentInvoker.InvokeAsync(ViewComponentContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.DefaultViewComponentHelper.InvokeCoreAsync(ViewComponentDescriptor descriptor, Object arguments)\r\n   at AspNetCore.Views_Partials_Website_Blocks_Form.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\Blocks\\Form.cshtml:line 38\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_Partials_Website_BlockContainers_ComponentSection.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\BlockContainers\\ComponentSection.cshtml:line 71\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_Partials_Website_BlockContainers_BlockComponents.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\BlockContainers\\BlockComponents.cshtml:line 18\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_USNPage.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\USNPage.cshtml:line 19\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)\r\n   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)\r\n   at Umbraco.Cms.Web.Common.Middleware.BasicAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.BackOffice.Middleware.BackOfficeExternalLoginProviderErrorMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAFDS2ORF7:0000002B","RequestPath":"/contact","ConnectionId":"0HMOAFDS2ORF7","ProcessId":7172,"ProcessName":"CruzDiez","ThreadId":33,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T08:57:23.0483285Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":13588,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:57:23.0597171Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":13588,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:57:23.0651561Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":13588,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:57:23.0652504Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":13588,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:57:23.0652671Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":13588,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T08:57:36.5861275Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAFEKE8RQH:00000029","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMOAFEKE8RQH","ProcessId":13588,"ProcessName":"CruzDiez","ThreadId":10,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T08:57:48.7549626Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidCastException: Unable to cast object of type 'Umbraco.Cms.Web.Common.PublishedModels.UsnglobalSettings' to type 'USNSiteBuilder.Core.Models.UmbracoFormsAppSettings'.\r\n   at AspNetCore.Views_Partials_Website_Forms_ContactForm.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\Forms\\ContactForm.cshtml:line 11\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.ViewViewComponentResult.ExecuteAsync(ViewComponentContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.DefaultViewComponentInvoker.InvokeAsync(ViewComponentContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.DefaultViewComponentHelper.InvokeCoreAsync(ViewComponentDescriptor descriptor, Object arguments)\r\n   at AspNetCore.Views_Partials_Website_Blocks_Form.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\Blocks\\Form.cshtml:line 38\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_Partials_Website_BlockContainers_ComponentSection.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\BlockContainers\\ComponentSection.cshtml:line 71\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_Partials_Website_BlockContainers_BlockComponents.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\BlockContainers\\BlockComponents.cshtml:line 18\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_USNPage.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\USNPage.cshtml:line 19\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)\r\n   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)\r\n   at Umbraco.Cms.Web.Common.Middleware.BasicAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.BackOffice.Middleware.BackOfficeExternalLoginProviderErrorMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAFEKE8RQH:0000002B","RequestPath":"/contact","ConnectionId":"0HMOAFEKE8RQH","ProcessId":13588,"ProcessName":"CruzDiez","ThreadId":10,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T08:58:42.5362114Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidCastException: Unable to cast object of type 'Umbraco.Cms.Web.Common.PublishedModels.UsnglobalSettings' to type 'USNSiteBuilder.Core.Models.UmbracoFormsAppSettings'.\r\n   at AspNetCore.Views_Partials_Website_Forms_ContactForm.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\Forms\\ContactForm.cshtml:line 11\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.ViewViewComponentResult.ExecuteAsync(ViewComponentContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.DefaultViewComponentInvoker.InvokeAsync(ViewComponentContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.DefaultViewComponentHelper.InvokeCoreAsync(ViewComponentDescriptor descriptor, Object arguments)\r\n   at AspNetCore.Views_Partials_Website_Blocks_Form.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\Blocks\\Form.cshtml:line 38\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_Partials_Website_BlockContainers_ComponentSection.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\BlockContainers\\ComponentSection.cshtml:line 71\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_Partials_Website_BlockContainers_BlockComponents.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\BlockContainers\\BlockComponents.cshtml:line 18\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_USNPage.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\USNPage.cshtml:line 19\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)\r\n   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)\r\n   at Umbraco.Cms.Web.Common.Middleware.BasicAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.BackOffice.Middleware.BackOfficeExternalLoginProviderErrorMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAFEKE8RQI:0000000F","RequestPath":"/contact","ConnectionId":"0HMOAFEKE8RQI","ProcessId":13588,"ProcessName":"CruzDiez","ThreadId":3,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T09:00:06.4296562Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":15740,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T09:00:06.4411485Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":15740,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T09:00:06.4458337Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":15740,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T09:00:06.4458875Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":15740,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T09:00:06.4458999Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":15740,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T09:00:19.8729538Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAFG541CHI:00000025","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMOAFG541CHI","ProcessId":15740,"ProcessName":"CruzDiez","ThreadId":9,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T09:00:31.7224902Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidCastException: Unable to cast object of type 'Umbraco.Cms.Web.Common.PublishedModels.UsnglobalSettings' to type 'USNSiteBuilder.Core.Models.UmbracoFormsAppSettings'.\r\n   at AspNetCore.Views_Partials_Website_Forms_ContactForm.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\Forms\\ContactForm.cshtml:line 11\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.ViewViewComponentResult.ExecuteAsync(ViewComponentContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.DefaultViewComponentInvoker.InvokeAsync(ViewComponentContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.DefaultViewComponentHelper.InvokeCoreAsync(ViewComponentDescriptor descriptor, Object arguments)\r\n   at AspNetCore.Views_Partials_Website_Blocks_Form.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\Blocks\\Form.cshtml:line 38\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_Partials_Website_BlockContainers_ComponentSection.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\BlockContainers\\ComponentSection.cshtml:line 71\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_Partials_Website_BlockContainers_BlockComponents.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\BlockContainers\\BlockComponents.cshtml:line 18\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_USNPage.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\USNPage.cshtml:line 19\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)\r\n   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)\r\n   at Umbraco.Cms.Web.Common.Middleware.BasicAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.BackOffice.Middleware.BackOfficeExternalLoginProviderErrorMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAFG541CHI:00000029","RequestPath":"/contact","ConnectionId":"0HMOAFG541CHI","ProcessId":15740,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T10:43:06.7147397Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":13084,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:43:06.7254144Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":13084,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:43:06.7301547Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":13084,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:43:06.7302066Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":13084,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:43:06.7302241Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":13084,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:43:20.0769343Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAH9MTLIHC:00000021","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMOAH9MTLIHC","ProcessId":13084,"ProcessName":"CruzDiez","ThreadId":8,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T10:51:17.4550693Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":1556,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:51:17.4699961Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":1556,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:51:17.4752879Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":1556,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:51:17.4753694Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":1556,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:51:17.4753842Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":1556,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:51:30.9985063Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAHE97TLHE:0000002B","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMOAHE97TLHE","ProcessId":1556,"ProcessName":"CruzDiez","ThreadId":3,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T10:52:40.4650647Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":4540,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:52:40.4768649Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":4540,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:52:40.4815768Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":4540,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:52:40.4816345Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":4540,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:52:40.4816484Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":4540,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:52:54.1649472Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAHF1VDRET:00000025","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMOAHF1VDRET","ProcessId":4540,"ProcessName":"CruzDiez","ThreadId":8,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T10:55:55.6202791Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":17784,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:55:55.6310372Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":17784,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:55:55.6357213Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":17784,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:55:55.6357716Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":17784,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:55:55.6357857Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":17784,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:56:09.1242567Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAHGS49KDU:00000025","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMOAHGS49KDU","ProcessId":17784,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T10:56:27.1174198Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.CompilationFailedException: One or more compilation failures occurred:\r\nC:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\Forms\\ContactForm.cshtml(124,98): error CS1061: 'UsnglobalSettings' does not contain a definition for 'GoogleReCaptchav2PublicKey' and no accessible extension method 'GoogleReCaptchav2PublicKey' accepting a first argument of type 'UsnglobalSettings' could be found (are you missing a using directive or an assembly reference?)\r\nC:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\Forms\\ContactForm.cshtml(124,154): error CS1061: 'UsnglobalSettings' does not contain a definition for 'GoogleReCaptchav2SecretKey' and no accessible extension method 'GoogleReCaptchav2SecretKey' accepting a first argument of type 'UsnglobalSettings' could be found (are you missing a using directive or an assembly reference?)\r\nC:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\Forms\\ContactForm.cshtml(126,89): error CS1061: 'UsnglobalSettings' does not contain a definition for 'PublicKey' and no accessible extension method 'PublicKey' accepting a first argument of type 'UsnglobalSettings' could be found (are you missing a using directive or an assembly reference?)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.RuntimeViewCompiler.CompileAndEmit(RazorCodeDocument codeDocument, String generatedCode)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.RuntimeViewCompiler.CompileAndEmit(String relativePath)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.RuntimeViewCompiler.OnCacheMiss(String normalizedPath)\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Razor.Compilation.DefaultRazorPageFactoryProvider.CreateFactory(String relativePath)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorViewEngine.CreateCacheResult(HashSet`1 expirationTokens, String relativePath, Boolean isMainPage)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorViewEngine.LocatePageFromPath(String executingFilePath, String pagePath, Boolean isMainPage)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorViewEngine.GetView(String executingFilePath, String viewPath, Boolean isMainPage)\r\n   at Umbraco.Cms.Web.Common.ModelsBuilder.RefreshingRazorViewEngine.GetView(String executingFilePath, String viewPath, Boolean isMainPage)\r\n   at Umbraco.Cms.Web.Website.ViewEngines.ProfilingViewEngine.GetView(String executingFilePath, String viewPath, Boolean isMainPage)\r\n   at Microsoft.AspNetCore.Mvc.ViewEngines.CompositeViewEngine.GetView(String executingFilePath, String viewPath, Boolean isMainPage)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.ViewViewComponentResult.ExecuteAsync(ViewComponentContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.DefaultViewComponentInvoker.InvokeAsync(ViewComponentContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.DefaultViewComponentHelper.InvokeCoreAsync(ViewComponentDescriptor descriptor, Object arguments)\r\n   at AspNetCore.Views_Partials_Website_Blocks_Form.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\Blocks\\Form.cshtml:line 38\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_Partials_Website_BlockContainers_ComponentSection.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\BlockContainers\\ComponentSection.cshtml:line 71\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_Partials_Website_BlockContainers_BlockComponents.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\BlockContainers\\BlockComponents.cshtml:line 18\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_USNPage.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\USNPage.cshtml:line 19\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)\r\n   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)\r\n   at Umbraco.Cms.Web.Common.Middleware.BasicAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.BackOffice.Middleware.BackOfficeExternalLoginProviderErrorMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAHGS49KDU:00000027","RequestPath":"/contact","ConnectionId":"0HMOAHGS49KDU","ProcessId":17784,"ProcessName":"CruzDiez","ThreadId":3,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T10:57:14.8861345Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":25112,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:57:14.8971497Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":25112,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:57:14.9020685Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":25112,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:57:14.9021304Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":25112,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:57:14.9021444Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":25112,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:57:28.4532159Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAHHJOCUOE:00000023","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMOAHHJOCUOE","ProcessId":25112,"ProcessName":"CruzDiez","ThreadId":10,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T10:57:33.5054849Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.CompilationFailedException: One or more compilation failures occurred:\r\nC:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\Forms\\ContactForm.cshtml(124,98): error CS1061: 'UsnglobalSettings' does not contain a definition for 'GoogleReCaptchav2PublicKey' and no accessible extension method 'GoogleReCaptchav2PublicKey' accepting a first argument of type 'UsnglobalSettings' could be found (are you missing a using directive or an assembly reference?)\r\nC:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\Forms\\ContactForm.cshtml(124,154): error CS1061: 'UsnglobalSettings' does not contain a definition for 'GoogleReCaptchav2PrivateKey' and no accessible extension method 'GoogleReCaptchav2PrivateKey' accepting a first argument of type 'UsnglobalSettings' could be found (are you missing a using directive or an assembly reference?)\r\nC:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\Forms\\ContactForm.cshtml(126,89): error CS1061: 'UsnglobalSettings' does not contain a definition for 'PublicKey' and no accessible extension method 'PublicKey' accepting a first argument of type 'UsnglobalSettings' could be found (are you missing a using directive or an assembly reference?)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.RuntimeViewCompiler.CompileAndEmit(RazorCodeDocument codeDocument, String generatedCode)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.RuntimeViewCompiler.CompileAndEmit(String relativePath)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.RuntimeViewCompiler.OnCacheMiss(String normalizedPath)\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Razor.Compilation.DefaultRazorPageFactoryProvider.CreateFactory(String relativePath)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorViewEngine.CreateCacheResult(HashSet`1 expirationTokens, String relativePath, Boolean isMainPage)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorViewEngine.LocatePageFromPath(String executingFilePath, String pagePath, Boolean isMainPage)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorViewEngine.GetView(String executingFilePath, String viewPath, Boolean isMainPage)\r\n   at Umbraco.Cms.Web.Common.ModelsBuilder.RefreshingRazorViewEngine.GetView(String executingFilePath, String viewPath, Boolean isMainPage)\r\n   at Umbraco.Cms.Web.Website.ViewEngines.ProfilingViewEngine.GetView(String executingFilePath, String viewPath, Boolean isMainPage)\r\n   at Microsoft.AspNetCore.Mvc.ViewEngines.CompositeViewEngine.GetView(String executingFilePath, String viewPath, Boolean isMainPage)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.ViewViewComponentResult.ExecuteAsync(ViewComponentContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.DefaultViewComponentInvoker.InvokeAsync(ViewComponentContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.DefaultViewComponentHelper.InvokeCoreAsync(ViewComponentDescriptor descriptor, Object arguments)\r\n   at AspNetCore.Views_Partials_Website_Blocks_Form.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\Blocks\\Form.cshtml:line 38\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_Partials_Website_BlockContainers_ComponentSection.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\BlockContainers\\ComponentSection.cshtml:line 71\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_Partials_Website_BlockContainers_BlockComponents.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\BlockContainers\\BlockComponents.cshtml:line 18\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_USNPage.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\USNPage.cshtml:line 19\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)\r\n   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)\r\n   at Umbraco.Cms.Web.Common.Middleware.BasicAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.BackOffice.Middleware.BackOfficeExternalLoginProviderErrorMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAHHJOCUOE:00000025","RequestPath":"/contact","ConnectionId":"0HMOAHHJOCUOE","ProcessId":25112,"ProcessName":"CruzDiez","ThreadId":3,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T10:59:06.1932007Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":11348,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:59:06.2043816Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":11348,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:59:06.2090139Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":11348,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:59:06.2090634Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":11348,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:59:06.2090761Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":11348,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T10:59:19.6769594Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAHIKUGEQ9:0000001D","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMOAHIKUGEQ9","ProcessId":11348,"ProcessName":"CruzDiez","ThreadId":11,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T10:59:23.7128299Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.CompilationFailedException: One or more compilation failures occurred:\r\nC:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\Forms\\ContactForm.cshtml(126,89): error CS1061: 'UsnglobalSettings' does not contain a definition for 'PublicKey' and no accessible extension method 'PublicKey' accepting a first argument of type 'UsnglobalSettings' could be found (are you missing a using directive or an assembly reference?)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.RuntimeViewCompiler.CompileAndEmit(RazorCodeDocument codeDocument, String generatedCode)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.RuntimeViewCompiler.CompileAndEmit(String relativePath)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.RuntimeViewCompiler.OnCacheMiss(String normalizedPath)\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Razor.Compilation.DefaultRazorPageFactoryProvider.CreateFactory(String relativePath)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorViewEngine.CreateCacheResult(HashSet`1 expirationTokens, String relativePath, Boolean isMainPage)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorViewEngine.LocatePageFromPath(String executingFilePath, String pagePath, Boolean isMainPage)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorViewEngine.GetView(String executingFilePath, String viewPath, Boolean isMainPage)\r\n   at Umbraco.Cms.Web.Common.ModelsBuilder.RefreshingRazorViewEngine.GetView(String executingFilePath, String viewPath, Boolean isMainPage)\r\n   at Umbraco.Cms.Web.Website.ViewEngines.ProfilingViewEngine.GetView(String executingFilePath, String viewPath, Boolean isMainPage)\r\n   at Microsoft.AspNetCore.Mvc.ViewEngines.CompositeViewEngine.GetView(String executingFilePath, String viewPath, Boolean isMainPage)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.ViewViewComponentResult.ExecuteAsync(ViewComponentContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.DefaultViewComponentInvoker.InvokeAsync(ViewComponentContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.DefaultViewComponentHelper.InvokeCoreAsync(ViewComponentDescriptor descriptor, Object arguments)\r\n   at AspNetCore.Views_Partials_Website_Blocks_Form.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\Blocks\\Form.cshtml:line 38\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_Partials_Website_BlockContainers_ComponentSection.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\BlockContainers\\ComponentSection.cshtml:line 71\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_Partials_Website_BlockContainers_BlockComponents.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\BlockContainers\\BlockComponents.cshtml:line 18\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_USNPage.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\USNPage.cshtml:line 19\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)\r\n   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)\r\n   at Umbraco.Cms.Web.Common.Middleware.BasicAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.BackOffice.Middleware.BackOfficeExternalLoginProviderErrorMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAHIKUGEQ9:00000021","RequestPath":"/contact","ConnectionId":"0HMOAHIKUGEQ9","ProcessId":11348,"ProcessName":"CruzDiez","ThreadId":27,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T11:00:19.1204895Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":28096,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T11:00:19.1313661Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":28096,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T11:00:19.1361687Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":28096,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T11:00:19.1362316Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":28096,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T11:00:19.1362447Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":28096,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T11:00:32.5175464Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAHJAL63IV:00000023","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMOAHJAL63IV","ProcessId":28096,"ProcessName":"CruzDiez","ThreadId":10,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T11:08:21.9593844Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":29024,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T11:08:21.9701890Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":29024,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T11:08:21.9748718Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":29024,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T11:08:21.9749275Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":29024,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T11:08:21.9749417Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":29024,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T11:08:35.9587306Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAHNQIUR3M:00000021","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMOAHNQIUR3M","ProcessId":29024,"ProcessName":"CruzDiez","ThreadId":25,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T11:13:43.9372644Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":27844,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T11:13:43.9480583Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":27844,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T11:13:43.9528066Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":27844,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T11:13:43.9528712Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":27844,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T11:13:43.9528855Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":27844,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T11:13:57.5275548Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAHQQHSMQU:00000023","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMOAHQQHSMQU","ProcessId":27844,"ProcessName":"CruzDiez","ThreadId":8,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T11:54:05.9594531Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":24664,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T11:54:05.9704979Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":24664,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T11:54:05.9754532Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":24664,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T11:54:05.9755221Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":24664,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T11:54:05.9755390Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":24664,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T11:54:19.1478845Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAIHCAG0EP:0000002F","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMOAIHCAG0EP","ProcessId":24664,"ProcessName":"CruzDiez","ThreadId":10,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T11:55:25.2620205Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":12612,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T11:55:25.2740679Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":12612,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T11:55:25.2800638Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":12612,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T11:55:25.2801490Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":12612,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T11:55:25.2801667Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":12612,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T11:55:38.7085762Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAII40D4AD:00000027","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMOAII40D4AD","ProcessId":12612,"ProcessName":"CruzDiez","ThreadId":3,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-09T12:09:37.1285823Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":7112,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T12:09:37.1405560Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":7112,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T12:09:37.1454234Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":7112,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T12:09:37.1455120Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":7112,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T12:09:37.1455318Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":7112,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-09T12:09:50.4796446Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOAIQ1ORU7P:00000021","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMOAIQ1ORU7P","ProcessId":7112,"ProcessName":"CruzDiez","ThreadId":3,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
