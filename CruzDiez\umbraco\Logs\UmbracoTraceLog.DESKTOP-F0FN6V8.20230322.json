{"@t":"2023-03-22T17:38:44.4028115Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":29068,"ProcessName":"CruzDiez","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-03-22T17:38:44.4152566Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":29068,"ProcessName":"<PERSON><PERSON><PERSON><PERSON>","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-03-22T17:38:44.4192858Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":29068,"ProcessName":"CruzDiez","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-03-22T17:38:44.4193677Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":29068,"ProcessName":"CruzDiez","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-03-22T17:38:44.4193913Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":29068,"ProcessName":"CruzDiez","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-03-22T17:38:45.3281760Z","@mt":"Umbraco must install or upgrade.","@l":"Warning","SourceContext":"Umbraco.Cms.Web.BackOffice.Install.InstallController","ActionId":"b0341b6d-298a-481b-9b9a-a5fd3ec34194","ActionName":"Umbraco.Cms.Web.BackOffice.Install.InstallController.Redirect (Umbraco.Web.BackOffice)","RequestId":"0HMPAVH4DSIP4:0000000F","RequestPath":"/","ConnectionId":"0HMPAVH4DSIP4","ProcessId":29068,"ProcessName":"CruzDiez","ThreadId":16,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"WARN ","HttpRequestId":"3f759de8-72db-48b4-819b-d3bc2c0a73f5","HttpRequestNumber":1,"HttpSessionId":"fa2b92df-f4c7-4e76-629c-37280fdeb690"}
{"@t":"2023-03-22T17:39:07.9895841Z","@mt":"Database configuration failed","@l":"Error","@x":"System.InvalidOperationException: The migration plan does not support migrating from state \"{3F5D492A-A3DB-43F9-A73E-9FEE3B180E6C}\".\r\n   at Umbraco.Cms.Infrastructure.Migrations.MigrationPlan.ThrowOnUnknownInitialState(String state)\r\n   at Umbraco.Cms.Infrastructure.Migrations.Upgrade.UmbracoPlan.ThrowOnUnknownInitialState(String state)\r\n   at Umbraco.Cms.Infrastructure.Migrations.MigrationPlanExecutor.Execute(MigrationPlan plan, String fromState)\r\n   at Umbraco.Cms.Infrastructure.Migrations.Upgrade.Upgrader.Execute(IMigrationPlanExecutor migrationPlanExecutor, IScopeProvider scopeProvider, IKeyValueService keyValueService)\r\n   at Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseBuilder.UpgradeSchemaAndData(UmbracoPlan plan)","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseBuilder","ActionId":"df1165a2-040b-4e3e-9ff6-504e0604fe5c","ActionName":"Umbraco.Cms.Web.BackOffice.Install.InstallApiController.PostPerformInstall (Umbraco.Web.BackOffice)","RequestId":"0HMPAVH4DSIP4:00000131","RequestPath":"/install/api/PostPerformInstall","ConnectionId":"0HMPAVH4DSIP4","ProcessId":29068,"ProcessName":"CruzDiez","ThreadId":46,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR","HttpRequestId":"ddcea167-7a6d-42f0-827f-2e4caf606511","HttpRequestNumber":2,"HttpSessionId":"9d5fe4ad-cd8b-5511-2de6-a2f6a9f76e05"}
{"@t":"2023-03-22T17:39:07.9985663Z","@mt":"Installation step {Step} failed.","@l":"Error","@x":"System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.\r\n ---> Umbraco.Cms.Core.Install.InstallException: The database failed to upgrade. ERROR: The database configuration failed with the following message: The migration plan does not support migrating from state \"{3F5D492A-A3DB-43F9-A73E-9FEE3B180E6C}\".\n Please check log file for additional information (can be found in '~/umbraco/Logs')\r\n   at Umbraco.Cms.Infrastructure.Install.InstallSteps.DatabaseUpgradeStep.ExecuteAsync(Object model)\r\n   --- End of inner exception stack trace ---\r\n   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)\r\n   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)\r\n   at Umbraco.Cms.Web.BackOffice.Install.InstallApiController.ExecuteStepAsync(InstallSetupStep step, Object instruction)\r\n   at Umbraco.Cms.Web.BackOffice.Install.InstallApiController.PostPerformInstall(InstallInstructions installModel)","Step":"DatabaseUpgrade","SourceContext":"Umbraco.Cms.Web.BackOffice.Install.InstallApiController","ActionId":"df1165a2-040b-4e3e-9ff6-504e0604fe5c","ActionName":"Umbraco.Cms.Web.BackOffice.Install.InstallApiController.PostPerformInstall (Umbraco.Web.BackOffice)","RequestId":"0HMPAVH4DSIP4:00000131","RequestPath":"/install/api/PostPerformInstall","ConnectionId":"0HMPAVH4DSIP4","ProcessId":29068,"ProcessName":"CruzDiez","ThreadId":46,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR","HttpRequestId":"ddcea167-7a6d-42f0-827f-2e4caf606511","HttpRequestNumber":2,"HttpSessionId":"9d5fe4ad-cd8b-5511-2de6-a2f6a9f76e05"}
{"@t":"2023-03-22T17:39:07.9990336Z","@mt":"An error occurred during installation step {Step}","@l":"Error","@x":"System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.\r\n ---> Umbraco.Cms.Core.Install.InstallException: The database failed to upgrade. ERROR: The database configuration failed with the following message: The migration plan does not support migrating from state \"{3F5D492A-A3DB-43F9-A73E-9FEE3B180E6C}\".\n Please check log file for additional information (can be found in '~/umbraco/Logs')\r\n   at Umbraco.Cms.Infrastructure.Install.InstallSteps.DatabaseUpgradeStep.ExecuteAsync(Object model)\r\n   --- End of inner exception stack trace ---\r\n   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)\r\n   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)\r\n   at Umbraco.Cms.Web.BackOffice.Install.InstallApiController.ExecuteStepAsync(InstallSetupStep step, Object instruction)\r\n   at Umbraco.Cms.Web.BackOffice.Install.InstallApiController.PostPerformInstall(InstallInstructions installModel)","Step":"DatabaseUpgrade","SourceContext":"Umbraco.Cms.Web.BackOffice.Install.InstallApiController","ActionId":"df1165a2-040b-4e3e-9ff6-504e0604fe5c","ActionName":"Umbraco.Cms.Web.BackOffice.Install.InstallApiController.PostPerformInstall (Umbraco.Web.BackOffice)","RequestId":"0HMPAVH4DSIP4:00000131","RequestPath":"/install/api/PostPerformInstall","ConnectionId":"0HMPAVH4DSIP4","ProcessId":29068,"ProcessName":"CruzDiez","ThreadId":46,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR","HttpRequestId":"ddcea167-7a6d-42f0-827f-2e4caf606511","HttpRequestNumber":2,"HttpSessionId":"9d5fe4ad-cd8b-5511-2de6-a2f6a9f76e05"}
{"@t":"2023-03-22T17:39:28.9712968Z","@mt":"Database configuration failed","@l":"Error","@x":"System.InvalidOperationException: The migration plan does not support migrating from state \"{3F5D492A-A3DB-43F9-A73E-9FEE3B180E6C}\".\r\n   at Umbraco.Cms.Infrastructure.Migrations.MigrationPlan.ThrowOnUnknownInitialState(String state)\r\n   at Umbraco.Cms.Infrastructure.Migrations.Upgrade.UmbracoPlan.ThrowOnUnknownInitialState(String state)\r\n   at Umbraco.Cms.Infrastructure.Migrations.MigrationPlanExecutor.Execute(MigrationPlan plan, String fromState)\r\n   at Umbraco.Cms.Infrastructure.Migrations.Upgrade.Upgrader.Execute(IMigrationPlanExecutor migrationPlanExecutor, IScopeProvider scopeProvider, IKeyValueService keyValueService)\r\n   at Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseBuilder.UpgradeSchemaAndData(UmbracoPlan plan)","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseBuilder","ActionId":"df1165a2-040b-4e3e-9ff6-504e0604fe5c","ActionName":"Umbraco.Cms.Web.BackOffice.Install.InstallApiController.PostPerformInstall (Umbraco.Web.BackOffice)","RequestId":"0HMPAVH4DSIP4:00000133","RequestPath":"/install/api/PostPerformInstall","ConnectionId":"0HMPAVH4DSIP4","ProcessId":29068,"ProcessName":"CruzDiez","ThreadId":18,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR","HttpRequestId":"e425f6d6-dd26-43a1-a56c-28f8b66b68d2","HttpRequestNumber":3,"HttpSessionId":"afd578ca-9426-d8cc-6816-efc183d607be"}
{"@t":"2023-03-22T17:39:28.9719935Z","@mt":"Installation step {Step} failed.","@l":"Error","@x":"System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.\r\n ---> Umbraco.Cms.Core.Install.InstallException: The database failed to upgrade. ERROR: The database configuration failed with the following message: The migration plan does not support migrating from state \"{3F5D492A-A3DB-43F9-A73E-9FEE3B180E6C}\".\n Please check log file for additional information (can be found in '~/umbraco/Logs')\r\n   at Umbraco.Cms.Infrastructure.Install.InstallSteps.DatabaseUpgradeStep.ExecuteAsync(Object model)\r\n   --- End of inner exception stack trace ---\r\n   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)\r\n   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)\r\n   at Umbraco.Cms.Web.BackOffice.Install.InstallApiController.ExecuteStepAsync(InstallSetupStep step, Object instruction)\r\n   at Umbraco.Cms.Web.BackOffice.Install.InstallApiController.PostPerformInstall(InstallInstructions installModel)","Step":"DatabaseUpgrade","SourceContext":"Umbraco.Cms.Web.BackOffice.Install.InstallApiController","ActionId":"df1165a2-040b-4e3e-9ff6-504e0604fe5c","ActionName":"Umbraco.Cms.Web.BackOffice.Install.InstallApiController.PostPerformInstall (Umbraco.Web.BackOffice)","RequestId":"0HMPAVH4DSIP4:00000133","RequestPath":"/install/api/PostPerformInstall","ConnectionId":"0HMPAVH4DSIP4","ProcessId":29068,"ProcessName":"CruzDiez","ThreadId":18,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR","HttpRequestId":"e425f6d6-dd26-43a1-a56c-28f8b66b68d2","HttpRequestNumber":3,"HttpSessionId":"afd578ca-9426-d8cc-6816-efc183d607be"}
{"@t":"2023-03-22T17:39:28.9723459Z","@mt":"An error occurred during installation step {Step}","@l":"Error","@x":"System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.\r\n ---> Umbraco.Cms.Core.Install.InstallException: The database failed to upgrade. ERROR: The database configuration failed with the following message: The migration plan does not support migrating from state \"{3F5D492A-A3DB-43F9-A73E-9FEE3B180E6C}\".\n Please check log file for additional information (can be found in '~/umbraco/Logs')\r\n   at Umbraco.Cms.Infrastructure.Install.InstallSteps.DatabaseUpgradeStep.ExecuteAsync(Object model)\r\n   --- End of inner exception stack trace ---\r\n   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)\r\n   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)\r\n   at Umbraco.Cms.Web.BackOffice.Install.InstallApiController.ExecuteStepAsync(InstallSetupStep step, Object instruction)\r\n   at Umbraco.Cms.Web.BackOffice.Install.InstallApiController.PostPerformInstall(InstallInstructions installModel)","Step":"DatabaseUpgrade","SourceContext":"Umbraco.Cms.Web.BackOffice.Install.InstallApiController","ActionId":"df1165a2-040b-4e3e-9ff6-504e0604fe5c","ActionName":"Umbraco.Cms.Web.BackOffice.Install.InstallApiController.PostPerformInstall (Umbraco.Web.BackOffice)","RequestId":"0HMPAVH4DSIP4:00000133","RequestPath":"/install/api/PostPerformInstall","ConnectionId":"0HMPAVH4DSIP4","ProcessId":29068,"ProcessName":"CruzDiez","ThreadId":18,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR","HttpRequestId":"e425f6d6-dd26-43a1-a56c-28f8b66b68d2","HttpRequestNumber":3,"HttpSessionId":"afd578ca-9426-d8cc-6816-efc183d607be"}
{"@t":"2023-03-22T17:39:39.8176315Z","@mt":"Database configuration failed","@l":"Error","@x":"System.InvalidOperationException: The migration plan does not support migrating from state \"{3F5D492A-A3DB-43F9-A73E-9FEE3B180E6C}\".\r\n   at Umbraco.Cms.Infrastructure.Migrations.MigrationPlan.ThrowOnUnknownInitialState(String state)\r\n   at Umbraco.Cms.Infrastructure.Migrations.Upgrade.UmbracoPlan.ThrowOnUnknownInitialState(String state)\r\n   at Umbraco.Cms.Infrastructure.Migrations.MigrationPlanExecutor.Execute(MigrationPlan plan, String fromState)\r\n   at Umbraco.Cms.Infrastructure.Migrations.Upgrade.Upgrader.Execute(IMigrationPlanExecutor migrationPlanExecutor, IScopeProvider scopeProvider, IKeyValueService keyValueService)\r\n   at Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseBuilder.UpgradeSchemaAndData(UmbracoPlan plan)","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseBuilder","ActionId":"df1165a2-040b-4e3e-9ff6-504e0604fe5c","ActionName":"Umbraco.Cms.Web.BackOffice.Install.InstallApiController.PostPerformInstall (Umbraco.Web.BackOffice)","RequestId":"0HMPAVH4DSIP4:0000013B","RequestPath":"/install/api/PostPerformInstall","ConnectionId":"0HMPAVH4DSIP4","ProcessId":29068,"ProcessName":"CruzDiez","ThreadId":3,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR","HttpRequestId":"6abf4288-b181-441b-ad12-479019bb2265","HttpRequestNumber":4,"HttpSessionId":"c08b503c-ae38-9a3b-dc67-f9860a69c05a"}
{"@t":"2023-03-22T17:39:39.8183162Z","@mt":"Installation step {Step} failed.","@l":"Error","@x":"System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.\r\n ---> Umbraco.Cms.Core.Install.InstallException: The database failed to upgrade. ERROR: The database configuration failed with the following message: The migration plan does not support migrating from state \"{3F5D492A-A3DB-43F9-A73E-9FEE3B180E6C}\".\n Please check log file for additional information (can be found in '~/umbraco/Logs')\r\n   at Umbraco.Cms.Infrastructure.Install.InstallSteps.DatabaseUpgradeStep.ExecuteAsync(Object model)\r\n   --- End of inner exception stack trace ---\r\n   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)\r\n   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)\r\n   at Umbraco.Cms.Web.BackOffice.Install.InstallApiController.ExecuteStepAsync(InstallSetupStep step, Object instruction)\r\n   at Umbraco.Cms.Web.BackOffice.Install.InstallApiController.PostPerformInstall(InstallInstructions installModel)","Step":"DatabaseUpgrade","SourceContext":"Umbraco.Cms.Web.BackOffice.Install.InstallApiController","ActionId":"df1165a2-040b-4e3e-9ff6-504e0604fe5c","ActionName":"Umbraco.Cms.Web.BackOffice.Install.InstallApiController.PostPerformInstall (Umbraco.Web.BackOffice)","RequestId":"0HMPAVH4DSIP4:0000013B","RequestPath":"/install/api/PostPerformInstall","ConnectionId":"0HMPAVH4DSIP4","ProcessId":29068,"ProcessName":"CruzDiez","ThreadId":3,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR","HttpRequestId":"6abf4288-b181-441b-ad12-479019bb2265","HttpRequestNumber":4,"HttpSessionId":"c08b503c-ae38-9a3b-dc67-f9860a69c05a"}
{"@t":"2023-03-22T17:39:39.8186659Z","@mt":"An error occurred during installation step {Step}","@l":"Error","@x":"System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.\r\n ---> Umbraco.Cms.Core.Install.InstallException: The database failed to upgrade. ERROR: The database configuration failed with the following message: The migration plan does not support migrating from state \"{3F5D492A-A3DB-43F9-A73E-9FEE3B180E6C}\".\n Please check log file for additional information (can be found in '~/umbraco/Logs')\r\n   at Umbraco.Cms.Infrastructure.Install.InstallSteps.DatabaseUpgradeStep.ExecuteAsync(Object model)\r\n   --- End of inner exception stack trace ---\r\n   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)\r\n   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)\r\n   at Umbraco.Cms.Web.BackOffice.Install.InstallApiController.ExecuteStepAsync(InstallSetupStep step, Object instruction)\r\n   at Umbraco.Cms.Web.BackOffice.Install.InstallApiController.PostPerformInstall(InstallInstructions installModel)","Step":"DatabaseUpgrade","SourceContext":"Umbraco.Cms.Web.BackOffice.Install.InstallApiController","ActionId":"df1165a2-040b-4e3e-9ff6-504e0604fe5c","ActionName":"Umbraco.Cms.Web.BackOffice.Install.InstallApiController.PostPerformInstall (Umbraco.Web.BackOffice)","RequestId":"0HMPAVH4DSIP4:0000013B","RequestPath":"/install/api/PostPerformInstall","ConnectionId":"0HMPAVH4DSIP4","ProcessId":29068,"ProcessName":"CruzDiez","ThreadId":3,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR","HttpRequestId":"6abf4288-b181-441b-ad12-479019bb2265","HttpRequestNumber":4,"HttpSessionId":"c08b503c-ae38-9a3b-dc67-f9860a69c05a"}
