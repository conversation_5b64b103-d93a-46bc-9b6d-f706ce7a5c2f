@using Umbraco.Cms.Core
@using Umbraco.Cms.Core.Models.PublishedContent
@using Umbraco.Cms.Core.Routing
@using Umbraco.Extensions
@inherits Umbraco.Cms.Web.Common.Macros.PartialViewMacroPage
@inject IPublishedValueFallback PublishedValueFallback
@inject IPublishedUrlProvider PublishedUrlProvider
@*
    This snippet creates links for every single page (no matter how deep) below
    the page currently being viewed by the website visitor, displayed as nested unordered HTML lists.
*@

@{ var selection = Model?.Content.Children.Where(x => x.IsVisible(PublishedValueFallback)).ToArray(); }

@* Ensure that the Current Page has children *@
@if (selection?.Length > 0)
{
    @* Get the first page in the children, where the property umbracoNaviHide is not True *@
    var naviLevel = selection[0].Level;

    @* Add in level for a CSS hook *@
    <ul class="level-@(naviLevel)">
        @* Loop through the selection *@
        @foreach (var item in selection)
        {
            <li>
                <a href="@item.Url(PublishedUrlProvider)">@item.Name</a>

                @* if this child page has any children, where the property umbracoNaviHide is not True *@
                @{
                    var children = item.Children.Where(x => x.IsVisible(PublishedValueFallback)).ToArray();
                    if (children.Length > 0)
                    {
                        @* Call a local method to display the children *@
                        ChildPages(children);
                    }
                }
            </li>
        }
    </ul>
}

@{
    void ChildPages(IPublishedContent[] selection)
    {
        //Ensure that we have a collection of pages
        if (selection.Length > 0)
        {
            // Get the first page in pages and get the level
            var naviLevel = selection[0].Level;

            // Add in level for a CSS hook
            <ul class="level-@(naviLevel)">
                @foreach (var item in selection)
                {
                    <li>
                        <a href="@item.Url(PublishedUrlProvider)">@item.Name</a>

                        @* if the page has any children, where the property umbracoNaviHide is not True *@
                        @{
                            var children = item.Children.Where(x => x.IsVisible(PublishedValueFallback)).ToArray();
                            if (children.Length > 0)
                            {
                                @* Recurse and call the ChildPages method to display the children *@
                                ChildPages(children);
                            }
                        }
                    </li>
                }
            </ul>
        }
    }
}
