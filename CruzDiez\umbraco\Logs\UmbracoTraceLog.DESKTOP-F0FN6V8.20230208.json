{"@t":"2023-02-08T08:24:34.2383272Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":6880,"ProcessName":"CruzDiez","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T08:24:34.2504779Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":6880,"ProcessName":"<PERSON><PERSON><PERSON><PERSON>","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T08:24:34.2543200Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":6880,"ProcessName":"CruzDiez","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T08:24:34.2543935Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":6880,"ProcessName":"CruzDiez","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T08:24:34.2544073Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":6880,"ProcessName":"CruzDiez","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T08:24:48.6602174Z","@mt":"No umbracoApplicationUrl for service (yet), skip.","@l":"Warning","SourceContext":"Umbraco.Cms.Infrastructure.HostedServices.ServerRegistration.TouchServerTask","ProcessId":6880,"ProcessName":"CruzDiez","ThreadId":14,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"WARN "}
{"@t":"2023-02-08T14:15:00.6956067Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":26660,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:15:00.7082219Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":26660,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:15:00.7137276Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":26660,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:15:00.7138043Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":26660,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:15:00.7138219Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":26660,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:15:58.3986116Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":22860,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:15:58.4101996Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":22860,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:15:58.4150299Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":22860,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:15:58.4150987Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":22860,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:15:58.4151119Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":22860,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:16:36.3171033Z","@mt":"Exception ({InstanceId}).","@l":"Error","@x":"Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'hasAccessToAllLanguages'.\r\n   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)\r\n   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)\r\n   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)\r\n   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()\r\n   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()\r\n   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)\r\n   at StackExchange.Profiling.Data.ProfiledDbCommand.ExecuteDbDataReader(CommandBehavior behavior) in C:\\projects\\dotnet\\src\\MiniProfiler.Shared\\Data\\ProfiledDbCommand.cs:line 216\r\n   at Umbraco.Cms.Infrastructure.Persistence.FaultHandling.RetryPolicy.ExecuteAction[TResult](Func`1 func)\r\n   at NPoco.Database.ExecuteReaderHelper(DbCommand cmd)\r\n   at NPoco.Database.ExecuteDataReader(DbCommand cmd, Boolean sync)\r\nClientConnectionId:16c2e9ae-a8d5-40fd-af31-ceba1fc4055e\r\nError Number:207,State:1,Class:16","InstanceId":"9d52ef13","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.UmbracoDatabase","ActionId":"************************************","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.AuthenticationController.PostLogin (Umbraco.Web.BackOffice)","RequestId":"0HMO9RS01V4RE:00000101","RequestPath":"/umbraco/backoffice/umbracoapi/authentication/PostLogin","ConnectionId":"0HMO9RS01V4RE","ProcessId":22860,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR","HttpRequestId":"c399fa32-a002-486a-98bf-cd7a3108abcb","HttpRequestNumber":1,"HttpSessionId":"276738c7-cc5c-8741-8032-5a4053cf99f9"}
{"@t":"2023-02-08T14:16:36.3701275Z","@mt":"Exception ({InstanceId}).","@l":"Error","@x":"Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid object name 'umbracoUserGroup2Language'.\r\n   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)\r\n   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)\r\n   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)\r\n   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()\r\n   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()\r\n   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)\r\n   at StackExchange.Profiling.Data.ProfiledDbCommand.ExecuteDbDataReader(CommandBehavior behavior) in C:\\projects\\dotnet\\src\\MiniProfiler.Shared\\Data\\ProfiledDbCommand.cs:line 216\r\n   at Umbraco.Cms.Infrastructure.Persistence.FaultHandling.RetryPolicy.ExecuteAction[TResult](Func`1 func)\r\n   at NPoco.Database.ExecuteReaderHelper(DbCommand cmd)\r\n   at NPoco.Database.ExecuteDataReader(DbCommand cmd, Boolean sync)\r\nClientConnectionId:16c2e9ae-a8d5-40fd-af31-ceba1fc4055e\r\nError Number:208,State:1,Class:16","InstanceId":"9d52ef13","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.UmbracoDatabase","ActionId":"************************************","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.AuthenticationController.PostLogin (Umbraco.Web.BackOffice)","RequestId":"0HMO9RS01V4RE:00000101","RequestPath":"/umbraco/backoffice/umbracoapi/authentication/PostLogin","ConnectionId":"0HMO9RS01V4RE","ProcessId":22860,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR","HttpRequestId":"c399fa32-a002-486a-98bf-cd7a3108abcb","HttpRequestNumber":1,"HttpSessionId":"276738c7-cc5c-8741-8032-5a4053cf99f9"}
{"@t":"2023-02-08T14:16:36.5031961Z","@mt":"Exception ({InstanceId}).","@l":"Error","@x":"Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'hasAccessToAllLanguages'.\r\n   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)\r\n   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)\r\n   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)\r\n   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()\r\n   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()\r\n   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)\r\n   at StackExchange.Profiling.Data.ProfiledDbCommand.ExecuteDbDataReader(CommandBehavior behavior) in C:\\projects\\dotnet\\src\\MiniProfiler.Shared\\Data\\ProfiledDbCommand.cs:line 216\r\n   at Umbraco.Cms.Infrastructure.Persistence.FaultHandling.RetryPolicy.ExecuteAction[TResult](Func`1 func)\r\n   at NPoco.Database.ExecuteReaderHelper(DbCommand cmd)\r\n   at NPoco.Database.ExecuteDataReader(DbCommand cmd, Boolean sync)\r\nClientConnectionId:16c2e9ae-a8d5-40fd-af31-ceba1fc4055e\r\nError Number:207,State:1,Class:16","InstanceId":"bd3b9e78","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.UmbracoDatabase","ActionId":"************************************","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.AuthenticationController.PostLogin (Umbraco.Web.BackOffice)","RequestId":"0HMO9RS01V4RE:00000101","RequestPath":"/umbraco/backoffice/umbracoapi/authentication/PostLogin","ConnectionId":"0HMO9RS01V4RE","ProcessId":22860,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR","HttpRequestId":"c399fa32-a002-486a-98bf-cd7a3108abcb","HttpRequestNumber":1,"HttpSessionId":"276738c7-cc5c-8741-8032-5a4053cf99f9"}
{"@t":"2023-02-08T14:16:36.5111419Z","@mt":"Exception ({InstanceId}).","@l":"Error","@x":"Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid object name 'umbracoUserGroup2Language'.\r\n   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)\r\n   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)\r\n   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)\r\n   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()\r\n   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()\r\n   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)\r\n   at StackExchange.Profiling.Data.ProfiledDbCommand.ExecuteDbDataReader(CommandBehavior behavior) in C:\\projects\\dotnet\\src\\MiniProfiler.Shared\\Data\\ProfiledDbCommand.cs:line 216\r\n   at Umbraco.Cms.Infrastructure.Persistence.FaultHandling.RetryPolicy.ExecuteAction[TResult](Func`1 func)\r\n   at NPoco.Database.ExecuteReaderHelper(DbCommand cmd)\r\n   at NPoco.Database.ExecuteDataReader(DbCommand cmd, Boolean sync)\r\nClientConnectionId:16c2e9ae-a8d5-40fd-af31-ceba1fc4055e\r\nError Number:208,State:1,Class:16","InstanceId":"bd3b9e78","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.UmbracoDatabase","ActionId":"************************************","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.AuthenticationController.PostLogin (Umbraco.Web.BackOffice)","RequestId":"0HMO9RS01V4RE:00000101","RequestPath":"/umbraco/backoffice/umbracoapi/authentication/PostLogin","ConnectionId":"0HMO9RS01V4RE","ProcessId":22860,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR","HttpRequestId":"c399fa32-a002-486a-98bf-cd7a3108abcb","HttpRequestNumber":1,"HttpSessionId":"276738c7-cc5c-8741-8032-5a4053cf99f9"}
{"@t":"2023-02-08T14:16:36.5389655Z","@mt":"Exception ({InstanceId}).","@l":"Error","@x":"Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'hasAccessToAllLanguages'.\r\n   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)\r\n   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)\r\n   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)\r\n   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()\r\n   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()\r\n   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)\r\n   at StackExchange.Profiling.Data.ProfiledDbCommand.ExecuteDbDataReader(CommandBehavior behavior) in C:\\projects\\dotnet\\src\\MiniProfiler.Shared\\Data\\ProfiledDbCommand.cs:line 216\r\n   at Umbraco.Cms.Infrastructure.Persistence.FaultHandling.RetryPolicy.ExecuteAction[TResult](Func`1 func)\r\n   at NPoco.Database.ExecuteReaderHelper(DbCommand cmd)\r\n   at NPoco.Database.ExecuteDataReader(DbCommand cmd, Boolean sync)\r\nClientConnectionId:16c2e9ae-a8d5-40fd-af31-ceba1fc4055e\r\nError Number:207,State:1,Class:16","InstanceId":"32c7f6c2","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.UmbracoDatabase","ActionId":"************************************","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.AuthenticationController.PostLogin (Umbraco.Web.BackOffice)","RequestId":"0HMO9RS01V4RE:00000101","RequestPath":"/umbraco/backoffice/umbracoapi/authentication/PostLogin","ConnectionId":"0HMO9RS01V4RE","ProcessId":22860,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR","HttpRequestId":"c399fa32-a002-486a-98bf-cd7a3108abcb","HttpRequestNumber":1,"HttpSessionId":"276738c7-cc5c-8741-8032-5a4053cf99f9"}
{"@t":"2023-02-08T14:16:36.5438766Z","@mt":"Exception ({InstanceId}).","@l":"Error","@x":"Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid object name 'umbracoUserGroup2Language'.\r\n   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)\r\n   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)\r\n   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)\r\n   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()\r\n   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()\r\n   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)\r\n   at StackExchange.Profiling.Data.ProfiledDbCommand.ExecuteDbDataReader(CommandBehavior behavior) in C:\\projects\\dotnet\\src\\MiniProfiler.Shared\\Data\\ProfiledDbCommand.cs:line 216\r\n   at Umbraco.Cms.Infrastructure.Persistence.FaultHandling.RetryPolicy.ExecuteAction[TResult](Func`1 func)\r\n   at NPoco.Database.ExecuteReaderHelper(DbCommand cmd)\r\n   at NPoco.Database.ExecuteDataReader(DbCommand cmd, Boolean sync)\r\nClientConnectionId:16c2e9ae-a8d5-40fd-af31-ceba1fc4055e\r\nError Number:208,State:1,Class:16","InstanceId":"32c7f6c2","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.UmbracoDatabase","ActionId":"************************************","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.AuthenticationController.PostLogin (Umbraco.Web.BackOffice)","RequestId":"0HMO9RS01V4RE:00000101","RequestPath":"/umbraco/backoffice/umbracoapi/authentication/PostLogin","ConnectionId":"0HMO9RS01V4RE","ProcessId":22860,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR","HttpRequestId":"c399fa32-a002-486a-98bf-cd7a3108abcb","HttpRequestNumber":1,"HttpSessionId":"276738c7-cc5c-8741-8032-5a4053cf99f9"}
{"@t":"2023-02-08T14:16:36.5565300Z","@mt":"Exception ({InstanceId}).","@l":"Error","@x":"Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'hasAccessToAllLanguages'.\r\n   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)\r\n   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)\r\n   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)\r\n   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()\r\n   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()\r\n   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)\r\n   at StackExchange.Profiling.Data.ProfiledDbCommand.ExecuteDbDataReader(CommandBehavior behavior) in C:\\projects\\dotnet\\src\\MiniProfiler.Shared\\Data\\ProfiledDbCommand.cs:line 216\r\n   at Umbraco.Cms.Infrastructure.Persistence.FaultHandling.RetryPolicy.ExecuteAction[TResult](Func`1 func)\r\n   at NPoco.Database.ExecuteReaderHelper(DbCommand cmd)\r\n   at NPoco.Database.ExecuteDataReader(DbCommand cmd, Boolean sync)\r\nClientConnectionId:16c2e9ae-a8d5-40fd-af31-ceba1fc4055e\r\nError Number:207,State:1,Class:16","InstanceId":"823d0f7b","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.UmbracoDatabase","ActionId":"************************************","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.AuthenticationController.PostLogin (Umbraco.Web.BackOffice)","RequestId":"0HMO9RS01V4RE:00000101","RequestPath":"/umbraco/backoffice/umbracoapi/authentication/PostLogin","ConnectionId":"0HMO9RS01V4RE","ProcessId":22860,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR","HttpRequestId":"c399fa32-a002-486a-98bf-cd7a3108abcb","HttpRequestNumber":1,"HttpSessionId":"276738c7-cc5c-8741-8032-5a4053cf99f9"}
{"@t":"2023-02-08T14:16:36.5615480Z","@mt":"Exception ({InstanceId}).","@l":"Error","@x":"Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid object name 'umbracoUserGroup2Language'.\r\n   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)\r\n   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)\r\n   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)\r\n   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()\r\n   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()\r\n   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)\r\n   at StackExchange.Profiling.Data.ProfiledDbCommand.ExecuteDbDataReader(CommandBehavior behavior) in C:\\projects\\dotnet\\src\\MiniProfiler.Shared\\Data\\ProfiledDbCommand.cs:line 216\r\n   at Umbraco.Cms.Infrastructure.Persistence.FaultHandling.RetryPolicy.ExecuteAction[TResult](Func`1 func)\r\n   at NPoco.Database.ExecuteReaderHelper(DbCommand cmd)\r\n   at NPoco.Database.ExecuteDataReader(DbCommand cmd, Boolean sync)\r\nClientConnectionId:16c2e9ae-a8d5-40fd-af31-ceba1fc4055e\r\nError Number:208,State:1,Class:16","InstanceId":"823d0f7b","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.UmbracoDatabase","ActionId":"************************************","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.AuthenticationController.PostLogin (Umbraco.Web.BackOffice)","RequestId":"0HMO9RS01V4RE:00000101","RequestPath":"/umbraco/backoffice/umbracoapi/authentication/PostLogin","ConnectionId":"0HMO9RS01V4RE","ProcessId":22860,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR","HttpRequestId":"c399fa32-a002-486a-98bf-cd7a3108abcb","HttpRequestNumber":1,"HttpSessionId":"276738c7-cc5c-8741-8032-5a4053cf99f9"}
{"@t":"2023-02-08T14:16:36.6681559Z","@mt":"Exception ({InstanceId}).","@l":"Error","@x":"Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'hasAccessToAllLanguages'.\r\n   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)\r\n   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)\r\n   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)\r\n   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()\r\n   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()\r\n   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)\r\n   at StackExchange.Profiling.Data.ProfiledDbCommand.ExecuteDbDataReader(CommandBehavior behavior) in C:\\projects\\dotnet\\src\\MiniProfiler.Shared\\Data\\ProfiledDbCommand.cs:line 216\r\n   at Umbraco.Cms.Infrastructure.Persistence.FaultHandling.RetryPolicy.ExecuteAction[TResult](Func`1 func)\r\n   at NPoco.Database.ExecuteReaderHelper(DbCommand cmd)\r\n   at NPoco.Database.ExecuteDataReader(DbCommand cmd, Boolean sync)\r\nClientConnectionId:16c2e9ae-a8d5-40fd-af31-ceba1fc4055e\r\nError Number:207,State:1,Class:16","InstanceId":"d26eb37e","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.UmbracoDatabase","ActionId":"************************************","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.AuthenticationController.PostLogin (Umbraco.Web.BackOffice)","RequestId":"0HMO9RS01V4RE:00000101","RequestPath":"/umbraco/backoffice/umbracoapi/authentication/PostLogin","ConnectionId":"0HMO9RS01V4RE","ProcessId":22860,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR","HttpRequestId":"c399fa32-a002-486a-98bf-cd7a3108abcb","HttpRequestNumber":1,"HttpSessionId":"276738c7-cc5c-8741-8032-5a4053cf99f9"}
{"@t":"2023-02-08T14:16:36.6721369Z","@mt":"Exception ({InstanceId}).","@l":"Error","@x":"Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid object name 'umbracoUserGroup2Language'.\r\n   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)\r\n   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)\r\n   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)\r\n   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()\r\n   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()\r\n   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)\r\n   at StackExchange.Profiling.Data.ProfiledDbCommand.ExecuteDbDataReader(CommandBehavior behavior) in C:\\projects\\dotnet\\src\\MiniProfiler.Shared\\Data\\ProfiledDbCommand.cs:line 216\r\n   at Umbraco.Cms.Infrastructure.Persistence.FaultHandling.RetryPolicy.ExecuteAction[TResult](Func`1 func)\r\n   at NPoco.Database.ExecuteReaderHelper(DbCommand cmd)\r\n   at NPoco.Database.ExecuteDataReader(DbCommand cmd, Boolean sync)\r\nClientConnectionId:16c2e9ae-a8d5-40fd-af31-ceba1fc4055e\r\nError Number:208,State:1,Class:16","InstanceId":"d26eb37e","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.UmbracoDatabase","ActionId":"************************************","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.AuthenticationController.PostLogin (Umbraco.Web.BackOffice)","RequestId":"0HMO9RS01V4RE:00000101","RequestPath":"/umbraco/backoffice/umbracoapi/authentication/PostLogin","ConnectionId":"0HMO9RS01V4RE","ProcessId":22860,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR","HttpRequestId":"c399fa32-a002-486a-98bf-cd7a3108abcb","HttpRequestNumber":1,"HttpSessionId":"276738c7-cc5c-8741-8032-5a4053cf99f9"}
{"@t":"2023-02-08T14:16:36.6957495Z","@mt":"Exception ({InstanceId}).","@l":"Error","@x":"Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'hasAccessToAllLanguages'.\r\n   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)\r\n   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)\r\n   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)\r\n   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()\r\n   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()\r\n   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)\r\n   at StackExchange.Profiling.Data.ProfiledDbCommand.ExecuteDbDataReader(CommandBehavior behavior) in C:\\projects\\dotnet\\src\\MiniProfiler.Shared\\Data\\ProfiledDbCommand.cs:line 216\r\n   at Umbraco.Cms.Infrastructure.Persistence.FaultHandling.RetryPolicy.ExecuteAction[TResult](Func`1 func)\r\n   at NPoco.Database.ExecuteReaderHelper(DbCommand cmd)\r\n   at NPoco.Database.ExecuteDataReader(DbCommand cmd, Boolean sync)\r\nClientConnectionId:16c2e9ae-a8d5-40fd-af31-ceba1fc4055e\r\nError Number:207,State:1,Class:16","InstanceId":"54f7e29c","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.UmbracoDatabase","ActionId":"************************************","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.AuthenticationController.PostLogin (Umbraco.Web.BackOffice)","RequestId":"0HMO9RS01V4RE:00000101","RequestPath":"/umbraco/backoffice/umbracoapi/authentication/PostLogin","ConnectionId":"0HMO9RS01V4RE","ProcessId":22860,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR","HttpRequestId":"c399fa32-a002-486a-98bf-cd7a3108abcb","HttpRequestNumber":1,"HttpSessionId":"276738c7-cc5c-8741-8032-5a4053cf99f9"}
{"@t":"2023-02-08T14:16:36.7000874Z","@mt":"Exception ({InstanceId}).","@l":"Error","@x":"Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid object name 'umbracoUserGroup2Language'.\r\n   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)\r\n   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)\r\n   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)\r\n   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()\r\n   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()\r\n   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)\r\n   at StackExchange.Profiling.Data.ProfiledDbCommand.ExecuteDbDataReader(CommandBehavior behavior) in C:\\projects\\dotnet\\src\\MiniProfiler.Shared\\Data\\ProfiledDbCommand.cs:line 216\r\n   at Umbraco.Cms.Infrastructure.Persistence.FaultHandling.RetryPolicy.ExecuteAction[TResult](Func`1 func)\r\n   at NPoco.Database.ExecuteReaderHelper(DbCommand cmd)\r\n   at NPoco.Database.ExecuteDataReader(DbCommand cmd, Boolean sync)\r\nClientConnectionId:16c2e9ae-a8d5-40fd-af31-ceba1fc4055e\r\nError Number:208,State:1,Class:16","InstanceId":"54f7e29c","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.UmbracoDatabase","ActionId":"************************************","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.AuthenticationController.PostLogin (Umbraco.Web.BackOffice)","RequestId":"0HMO9RS01V4RE:00000101","RequestPath":"/umbraco/backoffice/umbracoapi/authentication/PostLogin","ConnectionId":"0HMO9RS01V4RE","ProcessId":22860,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR","HttpRequestId":"c399fa32-a002-486a-98bf-cd7a3108abcb","HttpRequestNumber":1,"HttpSessionId":"276738c7-cc5c-8741-8032-5a4053cf99f9"}
{"@t":"2023-02-08T14:16:36.7061145Z","@mt":"Exception ({InstanceId}).","@l":"Error","@x":"Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'hasAccessToAllLanguages'.\r\n   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)\r\n   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)\r\n   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)\r\n   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()\r\n   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()\r\n   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)\r\n   at StackExchange.Profiling.Data.ProfiledDbCommand.ExecuteDbDataReader(CommandBehavior behavior) in C:\\projects\\dotnet\\src\\MiniProfiler.Shared\\Data\\ProfiledDbCommand.cs:line 216\r\n   at Umbraco.Cms.Infrastructure.Persistence.FaultHandling.RetryPolicy.ExecuteAction[TResult](Func`1 func)\r\n   at NPoco.Database.ExecuteReaderHelper(DbCommand cmd)\r\n   at NPoco.Database.ExecuteDataReader(DbCommand cmd, Boolean sync)\r\nClientConnectionId:16c2e9ae-a8d5-40fd-af31-ceba1fc4055e\r\nError Number:207,State:1,Class:16","InstanceId":"ef54c9e0","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.UmbracoDatabase","ActionId":"************************************","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.AuthenticationController.PostLogin (Umbraco.Web.BackOffice)","RequestId":"0HMO9RS01V4RE:00000101","RequestPath":"/umbraco/backoffice/umbracoapi/authentication/PostLogin","ConnectionId":"0HMO9RS01V4RE","ProcessId":22860,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR","HttpRequestId":"c399fa32-a002-486a-98bf-cd7a3108abcb","HttpRequestNumber":1,"HttpSessionId":"276738c7-cc5c-8741-8032-5a4053cf99f9"}
{"@t":"2023-02-08T14:16:36.7111205Z","@mt":"Exception ({InstanceId}).","@l":"Error","@x":"Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid object name 'umbracoUserGroup2Language'.\r\n   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)\r\n   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)\r\n   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)\r\n   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()\r\n   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()\r\n   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)\r\n   at StackExchange.Profiling.Data.ProfiledDbCommand.ExecuteDbDataReader(CommandBehavior behavior) in C:\\projects\\dotnet\\src\\MiniProfiler.Shared\\Data\\ProfiledDbCommand.cs:line 216\r\n   at Umbraco.Cms.Infrastructure.Persistence.FaultHandling.RetryPolicy.ExecuteAction[TResult](Func`1 func)\r\n   at NPoco.Database.ExecuteReaderHelper(DbCommand cmd)\r\n   at NPoco.Database.ExecuteDataReader(DbCommand cmd, Boolean sync)\r\nClientConnectionId:16c2e9ae-a8d5-40fd-af31-ceba1fc4055e\r\nError Number:208,State:1,Class:16","InstanceId":"ef54c9e0","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.UmbracoDatabase","ActionId":"************************************","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.AuthenticationController.PostLogin (Umbraco.Web.BackOffice)","RequestId":"0HMO9RS01V4RE:00000101","RequestPath":"/umbraco/backoffice/umbracoapi/authentication/PostLogin","ConnectionId":"0HMO9RS01V4RE","ProcessId":22860,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR","HttpRequestId":"c399fa32-a002-486a-98bf-cd7a3108abcb","HttpRequestNumber":1,"HttpSessionId":"276738c7-cc5c-8741-8032-5a4053cf99f9"}
{"@t":"2023-02-08T14:16:36.7176718Z","@mt":"Exception ({InstanceId}).","@l":"Error","@x":"Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'hasAccessToAllLanguages'.\r\n   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)\r\n   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)\r\n   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)\r\n   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()\r\n   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()\r\n   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)\r\n   at StackExchange.Profiling.Data.ProfiledDbCommand.ExecuteDbDataReader(CommandBehavior behavior) in C:\\projects\\dotnet\\src\\MiniProfiler.Shared\\Data\\ProfiledDbCommand.cs:line 216\r\n   at Umbraco.Cms.Infrastructure.Persistence.FaultHandling.RetryPolicy.ExecuteAction[TResult](Func`1 func)\r\n   at NPoco.Database.ExecuteReaderHelper(DbCommand cmd)\r\n   at NPoco.Database.ExecuteDataReader(DbCommand cmd, Boolean sync)\r\nClientConnectionId:16c2e9ae-a8d5-40fd-af31-ceba1fc4055e\r\nError Number:207,State:1,Class:16","InstanceId":"b1f2d746","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.UmbracoDatabase","ActionId":"************************************","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.AuthenticationController.PostLogin (Umbraco.Web.BackOffice)","RequestId":"0HMO9RS01V4RE:00000101","RequestPath":"/umbraco/backoffice/umbracoapi/authentication/PostLogin","ConnectionId":"0HMO9RS01V4RE","ProcessId":22860,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR","HttpRequestId":"c399fa32-a002-486a-98bf-cd7a3108abcb","HttpRequestNumber":1,"HttpSessionId":"276738c7-cc5c-8741-8032-5a4053cf99f9"}
{"@t":"2023-02-08T14:16:36.7224545Z","@mt":"Exception ({InstanceId}).","@l":"Error","@x":"Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid object name 'umbracoUserGroup2Language'.\r\n   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)\r\n   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)\r\n   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)\r\n   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()\r\n   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()\r\n   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)\r\n   at StackExchange.Profiling.Data.ProfiledDbCommand.ExecuteDbDataReader(CommandBehavior behavior) in C:\\projects\\dotnet\\src\\MiniProfiler.Shared\\Data\\ProfiledDbCommand.cs:line 216\r\n   at Umbraco.Cms.Infrastructure.Persistence.FaultHandling.RetryPolicy.ExecuteAction[TResult](Func`1 func)\r\n   at NPoco.Database.ExecuteReaderHelper(DbCommand cmd)\r\n   at NPoco.Database.ExecuteDataReader(DbCommand cmd, Boolean sync)\r\nClientConnectionId:16c2e9ae-a8d5-40fd-af31-ceba1fc4055e\r\nError Number:208,State:1,Class:16","InstanceId":"b1f2d746","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.UmbracoDatabase","ActionId":"************************************","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.AuthenticationController.PostLogin (Umbraco.Web.BackOffice)","RequestId":"0HMO9RS01V4RE:00000101","RequestPath":"/umbraco/backoffice/umbracoapi/authentication/PostLogin","ConnectionId":"0HMO9RS01V4RE","ProcessId":22860,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR","HttpRequestId":"c399fa32-a002-486a-98bf-cd7a3108abcb","HttpRequestNumber":1,"HttpSessionId":"276738c7-cc5c-8741-8032-5a4053cf99f9"}
{"@t":"2023-02-08T14:16:36.7296026Z","@mt":"Exception ({InstanceId}).","@l":"Error","@x":"Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'hasAccessToAllLanguages'.\r\n   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)\r\n   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)\r\n   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)\r\n   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()\r\n   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()\r\n   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)\r\n   at StackExchange.Profiling.Data.ProfiledDbCommand.ExecuteDbDataReader(CommandBehavior behavior) in C:\\projects\\dotnet\\src\\MiniProfiler.Shared\\Data\\ProfiledDbCommand.cs:line 216\r\n   at Umbraco.Cms.Infrastructure.Persistence.FaultHandling.RetryPolicy.ExecuteAction[TResult](Func`1 func)\r\n   at NPoco.Database.ExecuteReaderHelper(DbCommand cmd)\r\n   at NPoco.Database.ExecuteDataReader(DbCommand cmd, Boolean sync)\r\nClientConnectionId:16c2e9ae-a8d5-40fd-af31-ceba1fc4055e\r\nError Number:207,State:1,Class:16","InstanceId":"e4083e4e","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.UmbracoDatabase","ActionId":"************************************","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.AuthenticationController.PostLogin (Umbraco.Web.BackOffice)","RequestId":"0HMO9RS01V4RE:00000101","RequestPath":"/umbraco/backoffice/umbracoapi/authentication/PostLogin","ConnectionId":"0HMO9RS01V4RE","ProcessId":22860,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR","HttpRequestId":"c399fa32-a002-486a-98bf-cd7a3108abcb","HttpRequestNumber":1,"HttpSessionId":"276738c7-cc5c-8741-8032-5a4053cf99f9"}
{"@t":"2023-02-08T14:16:36.7332843Z","@mt":"Exception ({InstanceId}).","@l":"Error","@x":"Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid object name 'umbracoUserGroup2Language'.\r\n   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)\r\n   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)\r\n   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)\r\n   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()\r\n   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()\r\n   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)\r\n   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)\r\n   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)\r\n   at StackExchange.Profiling.Data.ProfiledDbCommand.ExecuteDbDataReader(CommandBehavior behavior) in C:\\projects\\dotnet\\src\\MiniProfiler.Shared\\Data\\ProfiledDbCommand.cs:line 216\r\n   at Umbraco.Cms.Infrastructure.Persistence.FaultHandling.RetryPolicy.ExecuteAction[TResult](Func`1 func)\r\n   at NPoco.Database.ExecuteReaderHelper(DbCommand cmd)\r\n   at NPoco.Database.ExecuteDataReader(DbCommand cmd, Boolean sync)\r\nClientConnectionId:16c2e9ae-a8d5-40fd-af31-ceba1fc4055e\r\nError Number:208,State:1,Class:16","InstanceId":"e4083e4e","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.UmbracoDatabase","ActionId":"************************************","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.AuthenticationController.PostLogin (Umbraco.Web.BackOffice)","RequestId":"0HMO9RS01V4RE:00000101","RequestPath":"/umbraco/backoffice/umbracoapi/authentication/PostLogin","ConnectionId":"0HMO9RS01V4RE","ProcessId":22860,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR","HttpRequestId":"c399fa32-a002-486a-98bf-cd7a3108abcb","HttpRequestNumber":1,"HttpSessionId":"276738c7-cc5c-8741-8032-5a4053cf99f9"}
{"@t":"2023-02-08T14:28:05.8018815Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":2648,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:28:05.8128463Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":2648,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:28:05.8174829Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":2648,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:28:05.8175355Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":2648,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:28:05.8175515Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":2648,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:28:20.1793329Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMO9S2OQCLR7:0000004F","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMO9S2OQCLR7","ProcessId":2648,"ProcessName":"CruzDiez","ThreadId":11,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-08T14:28:31.8579193Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMO9S2OQCLR7:0000006B","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMO9S2OQCLR7","ProcessId":2648,"ProcessName":"CruzDiez","ThreadId":10,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-08T14:28:50.0497132Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMO9S2OQCLR7:000002DB","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMO9S2OQCLR7","ProcessId":2648,"ProcessName":"CruzDiez","ThreadId":10,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-08T14:45:43.5039874Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":28580,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:45:43.5148411Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":28580,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:45:43.5211284Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":28580,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:45:43.5211966Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":28580,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:45:43.5212162Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":28580,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:45:57.1258100Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMO9SCK1BJPR:00000025","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMO9SCK1BJPR","ProcessId":28580,"ProcessName":"CruzDiez","ThreadId":8,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-08T14:49:51.8234906Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":20036,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:49:51.8347484Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":20036,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:49:51.8395816Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":20036,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:49:51.8396402Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":20036,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:49:51.8396537Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":20036,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:50:06.3027348Z","@mt":"No umbracoApplicationUrl for service (yet), skip.","@l":"Warning","SourceContext":"Umbraco.Cms.Infrastructure.HostedServices.ServerRegistration.TouchServerTask","ProcessId":20036,"ProcessName":"CruzDiez","ThreadId":23,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"WARN "}
{"@t":"2023-02-08T14:50:35.4708813Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMO9SEU1AHTQ:00000025","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMO9SEU1AHTQ","ProcessId":20036,"ProcessName":"CruzDiez","ThreadId":8,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-08T14:54:42.5783343Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":24720,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:54:42.5893317Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":24720,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:54:42.5940276Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":24720,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:54:42.5940873Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":24720,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:54:42.5941009Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":24720,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:54:57.1888595Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMO9SHKM5PQR:00000035","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMO9SHKM5PQR","ProcessId":24720,"ProcessName":"CruzDiez","ThreadId":19,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-08T14:57:30.9225829Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":13008,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:57:30.9334725Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":13008,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:57:30.9383741Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":13008,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:57:30.9384428Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":13008,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:57:30.9384579Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":13008,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:57:45.3629167Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMO9SJ6RLEFQ:00000029","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMO9SJ6RLEFQ","ProcessId":13008,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-08T14:58:51.3774245Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":17628,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:58:51.3890084Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":17628,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:58:51.3938048Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":17628,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:58:51.3938694Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":17628,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:58:51.3938891Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":17628,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T14:59:05.6347248Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMO9SJUOOT5T:00000027","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMO9SJUOOT5T","ProcessId":17628,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-08T14:59:31.5234446Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMO9SJUOOT5T:0000007D","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMO9SJUOOT5T","ProcessId":17628,"ProcessName":"CruzDiez","ThreadId":49,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-08T14:59:38.4962643Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMO9SJUOOT5T:00000095","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMO9SJUOOT5T","ProcessId":17628,"ProcessName":"CruzDiez","ThreadId":8,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-08T15:04:53.7587407Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMO9SJUOOT60:0000002B","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMO9SJUOOT60","ProcessId":17628,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-08T15:05:08.5335688Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":20520,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:05:08.5447302Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":20520,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:05:08.5504753Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":20520,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:05:08.5505488Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":20520,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:05:08.5505639Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":20520,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:05:22.3836371Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMO9SNF9BPSI:00000025","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMO9SNF9BPSI","ProcessId":20520,"ProcessName":"CruzDiez","ThreadId":10,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-08T15:16:36.0255197Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":22884,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:16:36.0402056Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":22884,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:16:36.0466268Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":22884,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:16:36.0467232Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":22884,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:16:36.0467550Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":22884,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:16:49.1463918Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMO9STS1Q7FU:00000027","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMO9STS1Q7FU","ProcessId":22884,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-08T15:22:29.8835875Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":17704,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:22:29.8950026Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":17704,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:22:29.9012043Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":17704,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:22:29.9012760Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":17704,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:22:29.9012972Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":17704,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:22:43.6763580Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMO9T15KH9UL:00000029","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMO9T15KH9UL","ProcessId":17704,"ProcessName":"CruzDiez","ThreadId":10,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-08T15:49:13.1583488Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":25164,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:49:13.1700218Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":25164,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:49:13.1752138Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":25164,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:49:13.1753659Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":25164,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:49:13.1753876Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":25164,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:49:26.7181338Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMO9TG3AF9CN:0000002B","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMO9TG3AF9CN","ProcessId":25164,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-08T15:51:57.0321377Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":15652,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:51:57.0434041Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":15652,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:51:57.0480823Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":15652,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:51:57.0481351Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":15652,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:51:57.0481521Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":15652,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:52:10.7019445Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMO9THK8PFGB:00000029","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMO9THK8PFGB","ProcessId":15652,"ProcessName":"CruzDiez","ThreadId":11,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-08T15:56:14.5013065Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":10328,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:56:14.5125999Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":10328,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:56:14.5174449Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":10328,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:56:14.5175046Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":10328,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:56:14.5175218Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":10328,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T15:56:27.9874658Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMO9TK0T76KB:00000029","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMO9TK0T76KB","ProcessId":10328,"ProcessName":"CruzDiez","ThreadId":23,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-08T16:01:55.3473689Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":12120,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T16:01:55.3582226Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":12120,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T16:01:55.3629537Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":12120,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T16:01:55.3630064Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":12120,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T16:01:55.3630242Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":12120,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T16:02:09.3518228Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMO9TN6HFVR9:00000027","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMO9TN6HFVR9","ProcessId":12120,"ProcessName":"CruzDiez","ThreadId":8,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-08T16:06:10.5506657Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":20532,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T16:06:10.5618002Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":20532,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T16:06:10.5679776Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":20532,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T16:06:10.5680446Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":20532,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T16:06:10.5680652Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":20532,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T16:06:23.9379276Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMO9TPIIBMBT:00000027","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMO9TPIIBMBT","ProcessId":20532,"ProcessName":"CruzDiez","ThreadId":11,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-08T16:10:41.6879167Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":16716,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T16:10:41.6991066Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":16716,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T16:10:41.7038894Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":16716,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T16:10:41.7039415Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":16716,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T16:10:41.7039541Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":16716,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T16:10:55.5872739Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMO9TS3FA5HE:00000021","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMO9TS3FA5HE","ProcessId":16716,"ProcessName":"CruzDiez","ThreadId":3,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-08T16:11:22.8887752Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidCastException: Unable to cast object of type 'Umbraco.Cms.Web.Common.PublishedModels.UsnglobalSettings' to type 'Umbraco.Cms.Core.Configuration.Models.GlobalSettings'.\r\n   at AspNetCore.Views_Partials_Website_Forms_ContactForm.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\Forms\\ContactForm.cshtml:line 10\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.ViewViewComponentResult.ExecuteAsync(ViewComponentContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.DefaultViewComponentInvoker.InvokeAsync(ViewComponentContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewComponents.DefaultViewComponentHelper.InvokeCoreAsync(ViewComponentDescriptor descriptor, Object arguments)\r\n   at AspNetCore.Views_Partials_Website_Blocks_Form.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\Blocks\\Form.cshtml:line 38\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_Partials_Website_BlockContainers_ComponentSection.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\BlockContainers\\ComponentSection.cshtml:line 71\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_Partials_Website_BlockContainers_BlockComponents.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\Partials\\Website\\BlockContainers\\BlockComponents.cshtml:line 18\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.RenderPartialCoreAsync(String partialViewName, Object model, ViewDataDictionary viewData, TextWriter writer)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.PartialAsync(String partialViewName, Object model, ViewDataDictionary viewData)\r\n   at AspNetCore.Views_USNPage.ExecuteAsync() in C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez\\Views\\USNPage.cshtml:line 19\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)\r\n   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)\r\n   at Umbraco.Cms.Web.Common.Middleware.BasicAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.BackOffice.Middleware.BackOfficeExternalLoginProviderErrorMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMO9TS3FA5HE:00000025","RequestPath":"/contact","ConnectionId":"0HMO9TS3FA5HE","ProcessId":16716,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-08T16:12:27.8039094Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":29292,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T16:12:27.8152517Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":29292,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T16:12:27.8199639Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":29292,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T16:12:27.8200173Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":29292,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T16:12:27.8200355Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":29292,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-08T16:12:42.2711708Z","@mt":"No umbracoApplicationUrl for service (yet), skip.","@l":"Warning","SourceContext":"Umbraco.Cms.Infrastructure.HostedServices.ServerRegistration.TouchServerTask","ProcessId":29292,"ProcessName":"CruzDiez","ThreadId":20,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"WARN "}
{"@t":"2023-02-08T16:12:59.3420761Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMO9TT31CDOG:00000029","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMO9TT31CDOG","ProcessId":29292,"ProcessName":"CruzDiez","ThreadId":12,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
