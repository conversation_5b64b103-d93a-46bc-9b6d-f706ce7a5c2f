{
  "$schema": "./umbraco/config/appsettings-schema.json",
  "Serilog": {
    "MinimumLevel": {
      "Default": "Error",
      "Override": {
        "Microsoft": "Error",
        "Microsoft.Hosting.Lifetime": "Warning",
        "System": "Error",
        "Umbraco": "Error"
      }
    }
  },
  "ConnectionStrings": {
    "umbracoDbDSN": "Data Source=WIN-9OBAO15KLS5;Initial Catalog=cruzdiezdev;Integrated Security=True"
  },
  "Umbraco": {
    "Storage": {
      "AzureBlob": {
        "Media": {
          "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=storagesharedasoftdev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
          "ContainerName": "cruzdiez"
        }
      }
    },
    "CMS": {
      "Global": {
        "ReservedPaths": "~/app_plugins/,~/install/,~/mini-profiler-resources/,~/umbraco/,~/error/",
        "Id": "b23e180d-82f3-4d6f-9727-4a44747eeb07",
        "SanitizeTinyMce": true,
        "Smtp": {
          "From": "<EMAIL>",
          "Host": "smtp.gmail.com",
          "Port": 587,
          //587,
          "SecureSocketOptions": "None",
          "Username": "<EMAIL>",
          "Password": "@W3b51t3_CD#"
        },
        "UseHttps": true
      },
      "Content": {
        "ContentVersionCleanupPolicy": {
          "EnableCleanup": true
        }
      }
    }
  },
  "uSkinned": {
    "SourceCodeOptions": "uSkinned,Website",
    "NotPages": "USNWebsiteConfigurationSection,USNGlobalSettings,USNFooter,USNNavigation,USNStylesFolder,USNStyle,USNBlogAuthor,USNBlogAuthors,USNBlogCategories,USNBlogMonthFolder,USNBlogPostsFolder,USNBlogYearFolder,USNReusablePods,USNReusableComponents,USNComponentGroup,USNPodGroup",
    "WebPEnabled": false
  }
}

