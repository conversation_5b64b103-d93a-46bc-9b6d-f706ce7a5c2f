@using Umbraco.Cms.Core
@using Umbraco.Cms.Core.Models.PublishedContent
@using Umbraco.Cms.Core.Routing
@using Umbraco.Extensions
@inherits Umbraco.Cms.Web.Common.Macros.PartialViewMacroPage
@inject IPublishedContentQuery PublishedContentQuery
@inject IPublishedValueFallback PublishedValueFallback
@inject IPublishedUrlProvider PublishedUrlProvider
@*
    Macro to list all child pages under a specific page in the content tree.

    How it works:
        - Confirm the startNodeId macro parameter has been passed in with a value
        - Loop through all child pages
        - Display a list of link to those pages, sorted by the value of the propertyAlias

    Macro Parameters To Create, for this macro to work:
    Alias:startNodeId     Name:Select starting page    Type:Content Picker
*@

@{ var startNodeId = Model?.MacroParameters["startNodeId"]; }

@if (startNodeId != null)
{
    @* Get the starting page *@
    var startNode = PublishedContentQuery.Content(startNodeId);
    var selection = startNode.Children.Where(x => x.IsVisible(PublishedValueFallback)).ToArray();

    if (selection.Length > 0)
    {
        <ul>
            @foreach (var item in selection)
            {
                <li><a href="@item.Url(PublishedUrlProvider)">@item.Name</a></li>
            }
        </ul>
    }
}
