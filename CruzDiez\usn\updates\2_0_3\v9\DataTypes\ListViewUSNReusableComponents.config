﻿<?xml version="1.0" encoding="utf-8"?>
<DataType Key="c67eeb1c-b556-417a-adae-264795a666cc" Alias="List View - USNReusableComponents" Level="3">
  <Info>
    <Name>List View - USNReusableComponents</Name>
    <EditorAlias>Umbraco.ListView</EditorAlias>
    <DatabaseType>Nvarchar</DatabaseType>
    <Folder>USN+Data+Types/List+View</Folder>
  </Info>
  <Config><![CDATA[{
  "PageSize": 10,
  "OrderBy": "sortOrder",
  "OrderDirection": "asc",
  "IncludeProperties": [
    {
      "alias": "updateDate",
      "header": "Last edited",
      "nameTemplate": null,
      "isSystem": 1
    },
    {
      "alias": "updater",
      "header": null,
      "nameTemplate": null,
      "isSystem": 1
    },
    {
      "alias": "owner",
      "header": null,
      "nameTemplate": null,
      "isSystem": 1
    },
    {
      "alias": "sortOrder",
      "header": null,
      "nameTemplate": null,
      "isSystem": 1
    },
    {
      "alias": "components",
      "header": "Components",
      "nameTemplate": null,
      "isSystem": 0
    }
  ],
  "Layouts": [
    {
      "name": "Components",
      "path": "/App_Plugins/uSkinned/views/components-listview.html",
      "icon": "icon-folder-outline color-orange",
      "isSystem": 0,
      "selected": true
    },
    {
      "name": "grid",
      "path": "views/propertyeditors/listview/layouts/grid/grid.html",
      "icon": "icon-thumbnails-small",
      "isSystem": 1,
      "selected": false
    },
    {
      "name": "List",
      "path": "views/propertyeditors/listview/layouts/list/list.html",
      "icon": "icon-list",
      "isSystem": 1,
      "selected": false
    }
  ],
  "BulkActionPermissions": {
    "allowBulkPublish": true,
    "allowBulkUnpublish": true,
    "allowBulkCopy": true,
    "allowBulkMove": true,
    "allowBulkDelete": true
  },
  "Icon": "icon-folder-outline color-orange",
  "TabName": "Groups",
  "ShowContentFirst": false,
  "UseInfiniteEditor": false
}]]></Config>
</DataType>