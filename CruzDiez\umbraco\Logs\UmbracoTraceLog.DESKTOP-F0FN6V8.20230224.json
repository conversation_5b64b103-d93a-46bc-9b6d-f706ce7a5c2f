{"@t":"2023-02-24T17:30:25.1396849Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":12652,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-24T17:30:25.1523001Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":12652,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-24T17:30:25.1573926Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":12652,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-24T17:30:25.1574676Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":12652,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-24T17:30:25.1574827Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":12652,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-24T17:30:41.7305492Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOMHJ8ABJSF:00000057","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMOMHJ8ABJSF","ProcessId":12652,"ProcessName":"CruzDiez","ThreadId":5,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-24T17:52:49.9959739Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":22800,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-24T17:52:50.0085788Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":22800,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-24T17:52:50.0136494Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":22800,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-24T17:52:50.0137221Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":22800,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-24T17:52:50.0137371Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":22800,"ProcessName":"CruzDiez","ThreadId":1,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-02-24T17:53:11.0393161Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOMHVP4UKM9:0000002D","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMOMHVP4UKM9","ProcessId":22800,"ProcessName":"CruzDiez","ThreadId":19,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-02-24T17:53:35.9541112Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"SixLabors.ImageSharp.ImageFormatException: Unexpected chunk followed VP8X header\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpThrowHelper.ThrowImageFormatException(String errorMessage)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.ReadVp8XHeader()\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoderCore.Decode[TPixel](BufferedReadStream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.ImageDecoderUtilities.Decode[TPixel](IImageDecoderInternals decoder, Configuration configuration, Stream stream, Func`3 largeImageExceptionFactory, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Formats.Webp.WebpDecoder.Decode[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.Decode[TPixel](Stream stream, Configuration config, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.<>c__DisplayClass132_0`1.<LoadWithFormatAsync>b__0(Stream s, CancellationToken ct)\r\n   at SixLabors.ImageSharp.Image.WithSeekableStreamAsync[T](Configuration configuration, Stream stream, Func`3 action, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Image.LoadWithFormatAsync[TPixel](Configuration configuration, Stream stream, CancellationToken cancellationToken)\r\n   at SixLabors.ImageSharp.Web.FormattedImage.LoadAsync[TPixel](Configuration configuration, Stream source)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.ProcessRequestAsync(HttpContext httpContext, IImageResolver sourceImageResolver, ImageContext imageContext, CommandCollection commands, Boolean retry)\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 119\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Forms.Web.HttpModules.ProtectFormUploadRequestsMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMOMHVP4UKM9:0000019F","RequestPath":"/media/zbql0mpw/banner_tuillerie.webp","ConnectionId":"0HMOMHVP4UKM9","ProcessId":22800,"ProcessName":"CruzDiez","ThreadId":11,"ApplicationId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
