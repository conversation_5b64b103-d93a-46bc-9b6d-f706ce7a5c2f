﻿@inherits UmbracoViewPage<SiteBuilderBaseViewModel>
@using USNSiteBuilder.Core.Models
@using Umbraco.Cms.Core.Models.Blocks
@{
    UsnglobalSettings globalSettings = (UsnglobalSettings)Model.GlobalSettings;

    if (!Model.Value<bool>("hideDefaultTopComponents") && globalSettings.Value<BlockListModel>("defaultTopComponents") != null)
    {
        BlockListModel pageComponents = globalSettings.Value<BlockListModel>("defaultTopComponents");

        if (pageComponents != null && pageComponents.Where(x => !x.Settings.Value<bool>("hideFromWebsite")).Any())
        {
            foreach (var component in pageComponents.Where(x => !x.Settings.Value<bool>("hideFromWebsite")))
            {
                if (component?.ContentUdi != null)
                {
                    var pageComponent = new SiteBuilderBlock(Model, component, "pageLayoutFull");

                    @await Html.PartialAsync(Model.ThemeFolder + "/BlockContainers/ComponentSection", pageComponent)
                }
            }
        }
    }
}
