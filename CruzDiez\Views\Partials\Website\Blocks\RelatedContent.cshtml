﻿@inherits UmbracoViewPage<SiteBuilderBlock>
@using USNSiteBuilder.Core.Models
@using USNSiteBuilder.Core.Extensions
@using USNSiteBuilder.Core.Interfaces
@using Umbraco.Cms.Core.Strings
@inject ISiteBuilderService _siteBuilderService
@{
    // Available as Pod Block type only

    var content = (Usn_Pb_RelatedContent)Model.BlockContent;

    //Settings taken from compositions common to all pod block types
    var uniqueSettings = (IUsn_Cmp_RelatedContent_Settings)Model.BlockSettings;
    var animateSettings = (IUsn_Cmp_AnimateSettings)Model.BlockSettings;
    var itemCommonBlockSettings = (IUsn_Cmp_CommonBlockSettings)Model.BlockSettings;

    string backgroundColorLabel = ViewData["backgroundColorLabel"].ToString();
    string itemClass = ViewData["itemClass"] != null ? ViewData["itemClass"].ToString() : String.Empty;
    string blockLocation = ViewData["blockLocation"] != null ? ViewData["blockLocation"].ToString() : String.Empty;
    string leftOffsetDesktop = ViewData["leftOffsetDesktop"] != null ? ViewData["leftOffsetDesktop"].ToString() : String.Empty;
    string leftOffsetTablet = ViewData["leftOffsetTablet"] != null ? ViewData["leftOffsetTablet"].ToString() : String.Empty;
    string colWidthDesktop = ViewData["colWidthDesktop"] != null ? ViewData["colWidthDesktop"].ToString() : String.Empty;
    string colWidthTablet = ViewData["colWidthTablet"] != null ? ViewData["colWidthTablet"].ToString() : String.Empty;

    //Clear view data before calling next partial
    ViewData.Clear();

    AnimationSettings animation = _siteBuilderService.GetAnimationSettings(animateSettings.Animate, animateSettings.AnimationDelay, animateSettings.AnimationDuration, animateSettings.AnimationStyle);

    if (content.RelatedContent.HasValue() && content.RelatedContent.Any())
    {
        string itemHeadingSizeClass = uniqueSettings.ItemHeadingSize;
        string itemSecondaryHeadingSizeClass = uniqueSettings.ItemSecondaryHeadingSize;

        Usnstyle websiteStyle = (Usnstyle)Model.BaseModel.WebsiteStyle;

        string overlayImageClass = uniqueSettings.TextPosition == "item_text-boxed item_overlay" ? _siteBuilderService.GetBackgroundColorClass(uniqueSettings.OverlayBackgroundColor.sortOrder, "content", websiteStyle) : String.Empty;
        backgroundColorLabel = uniqueSettings.TextPosition == "item_text-boxed item_overlay" ? uniqueSettings.OverlayBackgroundColor.label : backgroundColorLabel;
        string itemTextAlignment = uniqueSettings.TextAlignment.HasValue() ? uniqueSettings.TextAlignment : "text-left";
        ImageSettings imageSettings = _siteBuilderService.GetImageSettings(uniqueSettings.ImageStyle, uniqueSettings.BoxPad);
        string itemStyleClass = uniqueSettings.TextPosition.HasValue() ? uniqueSettings.TextPosition : "item_text-below";
        string blockLocationClass = blockLocation == "footer" ? "footer-item " + leftOffsetDesktop + " " + leftOffsetTablet + " " + colWidthDesktop + " " + colWidthTablet + " col-12 col" : String.Empty;
        string verticalAlignmentClass = uniqueSettings.TextPosition == "item_text-boxed item_overlay" ? (uniqueSettings.OverlayTextPosition.HasValue() ? uniqueSettings.OverlayTextPosition.ToString() : "align-self-center") : String.Empty;
        string itemBackgroundClass = uniqueSettings.TextPosition != "item_text-boxed item_overlay" && uniqueSettings.AddBackgroundColor ? "item_has-bg" : String.Empty;
        backgroundColorLabel = uniqueSettings.TextPosition != "item_text-boxed item_overlay" && uniqueSettings.AddBackgroundColor ? uniqueSettings.BackgroundColor.label : backgroundColorLabel;
        string innerBackgroundClass = uniqueSettings.TextPosition != "item_text-boxed item_overlay" && uniqueSettings.AddBackgroundColor ? _siteBuilderService.GetBackgroundColorClass(uniqueSettings.BackgroundColor.sortOrder, "content", websiteStyle) : String.Empty;

        foreach (var item in content.RelatedContent)
        {
            string pageName = item.HasValue("pageListingHeading") ? item.Value<string>("pageListingHeading") : item.Name;

            string podStyle = "usn_pod_" + _siteBuilderService.GetBlockStyleName(content.ContentType.Alias);

            <div class="item @itemBackgroundClass @blockLocationClass @itemClass @itemStyleClass @podStyle @itemTextAlignment @animation.AnimationClass @itemCommonBlockSettings.CustomClasses" data-os-animation="@animation.AnimationStyle" data-os-animation-delay="@animation.AnimationDelay" data-os-animation-duration="@animation.AnimationDuration">
                <div class="inner @innerBackgroundClass @overlayImageClass @imageSettings.CircleClass">
                    <a href="@item.Url()">
                        @if (item.Value<IPublishedContent>("pageListingImage") != null && uniqueSettings.TextPosition != "item_text-above")
                        {
                            <div class="image @overlayImageClass @imageSettings.CircleClass">
                                @await Html.PartialAsync(Model.BaseModel.ThemeFolder + "/MiscPageElements/Image", item.Value<IPublishedContent>("pageListingImage"), new ViewDataDictionary(ViewData) { { "imageSettings", imageSettings } })
                            </div>
                        }
                        <div class="info @verticalAlignmentClass">

                            @if (item.HasValue("pageListingSecondaryHeading") && websiteStyle.StyleLayout.headingOrder != "heading-first")
                            {
                                <p class="secondary-heading @itemSecondaryHeadingSizeClass @(backgroundColorLabel)-secondary-heading">@Html.Raw(item.Value<string>("pageListingSecondaryHeading"))</p>
                            }

                            <p class="heading @itemHeadingSizeClass @(backgroundColorLabel)-heading">@Html.Raw(pageName)</p>

                            @if (item.HasValue("pageListingSecondaryHeading") && websiteStyle.StyleLayout.headingOrder == "heading-first")
                            {
                                <p class="secondary-heading @itemSecondaryHeadingSizeClass @(backgroundColorLabel)-secondary-heading">@Html.Raw(item.Value<string>("pageListingSecondaryHeading"))</p>
                            }

                            @if (item.HasValue("pageSummary"))
                            {
                                <div class="text @(backgroundColorLabel)-text">@(item.Value<IHtmlEncodedString>("pageSummary"))</div>
                            }
                            @if (item.IsDocumentType(UsnblogPost.ModelTypeAlias) && uniqueSettings.TextPosition == "item_text-boxed item_overlay")
                            {
                                <!-- Meta -->
                                <div class="meta @(backgroundColorLabel)-text">
                                    <p class="date"><time>@(item.Value<DateTime>("postDate").ToString("dd MMM yyyy"))</time></p>
                                </div>
                                <!--// Meta -->
                            }
                        </div>
                        @if (item.Value<IPublishedContent>("pageListingImage") != null && uniqueSettings.TextPosition == "item_text-above")
                        {
                            <div class="image @overlayImageClass @imageSettings.CircleClass">
                                @await Html.PartialAsync(Model.BaseModel.ThemeFolder + "/MiscPageElements/Image", item.Value<IPublishedContent>("pageListingImage"), new ViewDataDictionary(ViewData) { { "imageSettings", imageSettings } })
                            </div>
                        }
                    </a>
                    @if (item.IsDocumentType(UsnblogPost.ModelTypeAlias) && uniqueSettings.TextPosition != "item_text-boxed item_overlay")
                    {
                        <!-- Meta -->
                        <div class="meta @(backgroundColorLabel)-text">
                            <p class="date"><time>@(item.Value<DateTime>("postDate").ToString("dd MMM yyyy"))</time></p>
                        </div>
                        <!--// Meta -->
                    }
                </div>
            </div>
        }
    }
}