<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<language alias="zh" intName="Chinese (Simple)" localName="中文（简体，中国）" lcid="0804" culture="zh-CN">
  <creator>
    <name>黄仁祥(<EMAIL>)</name>
    <link>https://our.umbraco.com/documentation/Extending-Umbraco/Language-Files</link>
  </creator>
  <area alias="actions">
    <key alias="assigndomain">管理主机名</key>
    <key alias="auditTrail">跟踪审计</key>
    <key alias="browse">浏览节点</key>
    <key alias="changeDocType">改变文档类型</key>
    <key alias="copy">复制</key>
    <key alias="create">创建</key>
    <key alias="createPackage">创建扩展包</key>
    <key alias="delete">删除</key>
    <key alias="disable">禁用</key>
    <key alias="emptyrecyclebin">清空回收站</key>
    <key alias="exportDocumentType">导出文档类型</key>
    <key alias="importdocumenttype">导入文档类型</key>
    <key alias="importPackage">导入扩展包</key>
    <key alias="liveEdit">实时编辑模式</key>
    <key alias="logout">退出</key>
    <key alias="move">移动</key>
    <key alias="notify">提醒</key>
    <key alias="protect">公众访问权限</key>
    <key alias="publish">发布</key>
    <key alias="unpublish">取消发布</key>
    <key alias="refreshNode">重新加载节点</key>
    <key alias="republish">重新发布整站</key>
    <key alias="restore" version="7.3.0">恢复</key>
    <key alias="chooseWhereToMove">选择移动目的地</key>
    <key alias="toInTheTreeStructureBelow">到下列的树结构中</key>
    <key alias="rights">权限</key>
    <key alias="rollback">回滚</key>
    <key alias="sendtopublish">提交至发布者</key>
    <key alias="sendToTranslate">发送给翻译</key>
    <key alias="sort">排序</key>
    <key alias="translate">翻译</key>
    <key alias="update">更新</key>
    <key alias="defaultValue">默认值</key>
  </area>
  <area alias="assignDomain">
    <key alias="permissionDenied">禁止访问</key>
    <key alias="addNew">添加域名</key>
    <key alias="remove">移除</key>
    <key alias="invalidNode">错误的节点</key>
    <key alias="invalidDomain">域名错误</key>
    <key alias="duplicateDomain">域名重复</key>
    <key alias="language">语言</key>
    <key alias="domain">域名</key>
    <key alias="domainCreated">新域名 '%0%' 已创建</key>
    <key alias="domainDeleted">域名 '%0%' 已删除</key>
    <key alias="domainExists">域名 '%0%' 已使用</key>
    <key alias="domainUpdated">域名 '%0%' 已更新</key>
    <key alias="orEdit">编辑当前域名</key>
    <key alias="inherit">继承</key>
    <key alias="setLanguage">语言</key>
    <key alias="setLanguageHelp"><![CDATA[为当前节点及子节点设置语言，<br />
       也可以从父节点继承。]]></key>
    <key alias="setDomains">域名</key>
  </area>
  <area alias="auditTrails">
    <key alias="atViewingFor">查看</key>
  </area>
  <area alias="buttons">
    <key alias="clearSelection">清除选择</key>
    <key alias="select">选择</key>
    <key alias="somethingElse">其它功能</key>
    <key alias="bold">粗体</key>
    <key alias="deindent">取消段落缩进</key>
    <key alias="formFieldInsert">插入表单字段</key>
    <key alias="graphicHeadline">插入图片标题</key>
    <key alias="htmlEdit">编辑Html</key>
    <key alias="indent">段落缩进</key>
    <key alias="italic">斜体</key>
    <key alias="justifyCenter">居中</key>
    <key alias="justifyLeft">左对齐</key>
    <key alias="justifyRight">右对齐</key>
    <key alias="linkInsert">插入链接</key>
    <key alias="linkLocal">插入本地链接（锚点）</key>
    <key alias="listBullet">圆点列表</key>
    <key alias="listNumeric">数字列表</key>
    <key alias="macroInsert">插入宏</key>
    <key alias="pictureInsert">插入图片</key>
    <key alias="relations">编辑关联</key>
    <key alias="returnToList">返回列表</key>
    <key alias="save">保存</key>
    <key alias="saveAndPublish">保存并发布</key>
    <key alias="saveToPublish">保存并提交审核</key>
    <key alias="saveListView">保存列表视图</key>
    <key alias="saveAndPreview">预览</key>
    <key alias="showPageDisabled">因未设置模板无法预览</key>
    <key alias="styleChoose">选择样式</key>
    <key alias="styleShow">显示样式</key>
    <key alias="tableInsert">插入表格</key>
  </area>
  <area alias="content">
    <key alias="isPublished" version="7.2">已发布</key>
    <key alias="about">关于本页</key>
    <key alias="alias">别名</key>
    <key alias="alternativeTextHelp">（图片的替代文本）</key>
    <key alias="alternativeUrls">替代链接</key>
    <key alias="clickToEdit">点击编辑</key>
    <key alias="createBy">创建者</key>
    <key alias="createByDesc" version="7.0">原作者</key>
    <key alias="updatedBy" version="7.0">更新者</key>
    <key alias="createDate">创建时间</key>
    <key alias="createDateDesc" version="7.0">创建此文档的日期/时间</key>
    <key alias="documentType">文档类型</key>
    <key alias="editing">编辑</key>
    <key alias="expireDate">过期于</key>
    <key alias="itemChanged">该项发布之后有更改</key>
    <key alias="itemNotPublished">该项没有发布</key>
    <key alias="lastPublished">最近发布</key>
    <key alias="noItemsToShow">没有要显示的项目</key>
    <key alias="listViewNoItems" version="7.1.5">列表中没有要显示的项目。</key>
    <key alias="mediatype">媒体类型</key>
    <key alias="mediaLinks">媒体链接地址</key>
    <key alias="membergroup">会员组</key>
    <key alias="memberrole">角色</key>
    <key alias="membertype">会员类型</key>
    <key alias="noDate">没有选择时间</key>
    <key alias="nodeName">页标题</key>
    <key alias="otherElements">属性</key>
    <key alias="parentNotPublished">该文档不可见，因为其上级 '%0%' 未发布。</key>
    <key alias="parentNotPublishedAnomaly">该文档已发布，但是没有更新至缓存（内部错误）</key>
    <key alias="getUrlException">Could not get the URL</key>
    <key alias="routeError">This document is published but its URL would collide with content %0%</key>
    <key alias="publish">发布</key>
    <key alias="publishStatus">发布状态</key>
    <key alias="releaseDate">发布于</key>
    <key alias="unpublishDate">取消发布于</key>
    <key alias="removeDate">清空时间</key>
    <key alias="sortDone">排序完成</key>
    <key alias="sortHelp">拖拽项目或单击列头即可排序，可以按住Shift多选。</key>
    <key alias="statistics">统计</key>
    <key alias="titleOptional">标题（可选）</key>
    <key alias="altTextOptional">Alternative text (optional)</key>
    <key alias="type">类型</key>
    <key alias="unpublish">取消发布</key>
    <key alias="updateDate">最近编辑</key>
    <key alias="updateDateDesc" version="7.0">编辑此文档的日期/时间</key>
    <key alias="uploadClear">移除文件</key>
    <key alias="urls">链接到文档</key>
    <key alias="memberof">会员组成员</key>
    <key alias="notmemberof">非会员组成员</key>
    <key alias="childItems" version="7.0">子项</key>
    <key alias="target" version="7.0">目标</key>
    <key alias="scheduledPublishServerTime">这将转换到服务器上的以下时间:</key>
    <key alias="scheduledPublishDocumentation"><![CDATA[<a href="https://our.umbraco.com/documentation/Getting-Started/Data/Scheduled-Publishing/#timezones" target="_blank" rel="noopener">这是什么意思？</a>]]></key>
  </area>
  <area alias="media">
    <key alias="clickToUpload">点击上传</key>
    <key alias="orClickHereToUpload">或单击此处选择文件</key>
    <key alias="maxFileSize">最大文件大小为</key>
  </area>
  <area alias="member">
    <key alias="createNewMember">创建新成员</key>
    <key alias="allMembers">所有成员</key>
  </area>
  <area alias="create">
    <key alias="chooseNode">您想在哪里创建 %0%</key>
    <key alias="createUnder">创建在</key>
    <key alias="updateData">选择类型和标题</key>
    <key alias="noDocumentTypes" version="7.0"><![CDATA[没有允许的文档类型可用。必须在 <strong> "文档类型" </strong> 下的 "设置" 部分中启用这些内容。]]></key>
    <key alias="noMediaTypes" version="7.0"><![CDATA[没有允许的媒体类型可用。必须在 <strong> "媒体类型" </strong> 下的 "设置" 部分中启用这些内容。]]></key>
    <key alias="documentTypeWithoutTemplate">没有模板的文档类型</key>
    <key alias="newFolder">新建文件夹</key>
    <key alias="newDataType">新数据类型</key>
  </area>
  <area alias="dashboard">
    <key alias="browser">浏览您的网站</key>
    <key alias="dontShowAgain">- 隐藏</key>
    <key alias="nothinghappens">如果Umbraco没有打开，您可能需要允许弹出式窗口。</key>
    <key alias="openinnew">已经在新窗口中打开</key>
    <key alias="restart">重启</key>
    <key alias="visit">访问</key>
    <key alias="welcome">欢迎</key>
  </area>
  <area alias="prompt">
    <key alias="stay">保持</key>
    <key alias="discardChanges">丢弃更改</key>
    <key alias="unsavedChanges">您有未保存的更改</key>
    <key alias="unsavedChangesWarning">确实要离开此页吗？-您有未保存的更改</key>
  </area>
  <area alias="bulk">
    <key alias="done">完成</key>
    <key alias="deletedItem">已删除 %0% 项</key>
    <key alias="deletedItems">已删除 %0% 项</key>
    <key alias="deletedItemOfItem">已删除 %0% 项，共 %1% 项</key>
    <key alias="deletedItemOfItems">已删除 %0% 项，共 %1% 项</key>
    <key alias="publishedItem">已发布 %0% 项</key>
    <key alias="publishedItems">已发布 %0% 项</key>
    <key alias="publishedItemOfItem">已发布 %0% 项，共 %1% 项</key>
    <key alias="publishedItemOfItems">已发布 %0% 项，共 %1% 项</key>
    <key alias="unpublishedItem">已取消发布 %0% 项</key>
    <key alias="unpublishedItems">已取消发布 %0% 项</key>
    <key alias="unpublishedItemOfItem">已取消发布 %0% 项，共 %1% 项</key>
    <key alias="unpublishedItemOfItems">已取消发布 %0% 项，共 %1% 项</key>
    <key alias="movedItem">已移动 %0% 项</key>
    <key alias="movedItems">已移动 %0% 项</key>
    <key alias="movedItemOfItem">已移动 %0% 项，共 %1% 项</key>
    <key alias="movedItemOfItems">已移动 %0% 项，共 %1% 项</key>
    <key alias="copiedItem">已复制 %0% 项</key>
    <key alias="copiedItems">已复制 %0% 项</key>
    <key alias="copiedItemOfItem">已复制 %0% 项，共 %1% 项</key>
    <key alias="copiedItemOfItems">已复制 %0% 项，共 %1% 项</key>
  </area>
  <area alias="defaultdialogs">
    <key alias="anchorInsert">锚点名称</key>
    <key alias="assignDomain">管理主机名</key>
    <key alias="closeThisWindow">关闭窗口</key>
    <key alias="confirmdelete">您确定要删除吗</key>
    <key alias="confirmdisable">您确定要禁用吗</key>
    <key alias="confirmlogout">您确定吗?</key>
    <key alias="confirmSure">您确定吗？</key>
    <key alias="cut">剪切</key>
    <key alias="editdictionary">编辑字典项</key>
    <key alias="editlanguage">编辑语言</key>
    <key alias="insertAnchor">插入本地链接</key>
    <key alias="insertCharacter">插入字符</key>
    <key alias="insertgraphicheadline">插入图片标题</key>
    <key alias="insertimage">插入图片</key>
    <key alias="insertlink">插入链接</key>
    <key alias="insertMacro">插入宏</key>
    <key alias="inserttable">插入表格</key>
    <key alias="lastEdited">最近编辑</key>
    <key alias="link">链接</key>
    <key alias="linkinternal">内部链接：</key>
    <key alias="linklocaltip">本地链接请用“#”号开头</key>
    <key alias="linknewwindow">在新窗口中打开？</key>
    <key alias="macroDoesNotHaveProperties"><![CDATA[该宏没有可编辑的属性]]></key>
    <key alias="paste">粘贴</key>
    <key alias="permissionsEdit">编辑权限</key>
    <key alias="recycleBinDeleting">正在清空回收站，请不要关闭窗口。</key>
    <key alias="recycleBinIsEmpty">回收站已清空</key>
    <key alias="recycleBinWarning">从回收站删除的项目将不可恢复</key>
    <key alias="regexSearchError"><![CDATA[<a target='_blank' rel='noopener' href='http://regexlib.com'>regexlib.com</a>的服务暂时出现问题。]]></key>
    <key alias="regexSearchHelp">查找正则表达式来验证输入，如: 'email、'zip-code'、'URL'。</key>
    <key alias="removeMacro">移除宏</key>
    <key alias="requiredField">必填项</key>
    <key alias="sitereindexed">站点已重建索引</key>
    <key alias="siterepublished">网站缓存已刷新，所有已发布的内容更新生效。</key>
    <key alias="siterepublishHelp">网站缓存将会刷新，所有已发布的内容将会更新。</key>
    <key alias="tableColumns">表格列数</key>
    <key alias="tableRows">表格行数</key>
    <key alias="thumbnailimageclickfororiginal">点击图片查看完整大小</key>
    <key alias="treepicker">拾取项</key>
    <key alias="viewCacheItem">查看缓存项</key>
    <key alias="relateToOriginalLabel">与原始连接</key>
    <key alias="includeDescendants">包括后代</key>
    <key alias="theFriendliestCommunity">最友好的社区</key>
    <key alias="linkToPage">链接到页面</key>
    <key alias="openInNewWindow">在新窗口或选项卡中打开链接的文档</key>
    <key alias="linkToMedia">链接到媒体</key>
    <key alias="selectMedia">选择媒体</key>
    <key alias="selectIcon">选择图标</key>
    <key alias="selectItem">选择项</key>
    <key alias="selectLink">选择链接</key>
    <key alias="selectMacro">选择宏</key>
    <key alias="selectContent">选择内容</key>
    <key alias="selectMember">选择成员</key>
    <key alias="selectMemberGroup">选择成员组</key>
    <key alias="noIconsFound">未找到图标</key>
    <key alias="noMacroParams">此宏没有参数</key>
    <key alias="externalLoginProviders">外部登录提供程序</key>
    <key alias="exceptionDetail">异常详细信息</key>
    <key alias="stacktrace">堆栈跟踪</key>
    <key alias="innerException">内部异常</key>
    <key alias="linkYour">链接您的</key>
    <key alias="unLinkYour">取消链接您的</key>
    <key alias="account">帐户</key>
    <key alias="selectEditor">选择编辑器</key>
  </area>
  <area alias="dictionaryItem">
    <key alias="description"><![CDATA[
       为字典项编辑不同语言的版本‘<em>%0%</em>’<br/>您可以在左侧的“语言”中添加一种语言
    ]]></key>
    <key alias="displayName">语言名称</key>
    <key alias="changeKeyError"><![CDATA[
      关键字 '%0%' 已经存在。
   ]]></key>
  </area>
  <area alias="placeholders">
    <key alias="username">输入您的用户名</key>
    <key alias="password">输入您的密码</key>
    <key alias="confirmPassword">确认密码</key>
    <key alias="nameentity">命名 %0%...</key>
    <key alias="entername">输入名称...</key>
    <key alias="label">标签...</key>
    <key alias="enterDescription">输入说明...</key>
    <key alias="search">输入搜索关键字...</key>
    <key alias="filter">输入过滤词...</key>
    <key alias="enterTags">键入添加tags (在每个tag之后按 enter)...</key>
    <key alias="email">输入您的电子邮件</key>
  </area>
  <area alias="editcontenttype">
    <key alias="createListView" version="7.2">创建自定义列表视图</key>
    <key alias="removeListView" version="7.2">删除自定义列表视图</key>
  </area>
  <area alias="editdatatype">
    <key alias="addPrevalue">添加预设值</key>
    <key alias="dataBaseDatatype">数据库数据类型</key>
    <key alias="guid">数据类型唯一标识</key>
    <key alias="renderControl">渲染控件</key>
    <key alias="rteButtons">按钮</key>
    <key alias="rteEnableAdvancedSettings">允许高级设置</key>
    <key alias="rteEnableContextMenu">允许快捷菜单</key>
    <key alias="rteMaximumDefaultImgSize">插入图片默认最大</key>
    <key alias="rteRelatedStylesheets">关联的样式表</key>
    <key alias="rteShowLabel">显示标签</key>
    <key alias="rteWidthAndHeight">宽和高</key>
  </area>
  <area alias="errorHandling">
    <key alias="errorButDataWasSaved">数据已保存，但是发布前您需要修正一些错误：</key>
    <key alias="errorChangingProviderPassword">当前成员提供程序不支持修改密码(EnablePasswordRetrieval的值应该为true)</key>
    <key alias="errorExistsWithoutTab">%0% 已存在</key>
    <key alias="errorHeader">发现错误：</key>
    <key alias="errorHeaderWithoutTab">发现错误：</key>
    <key alias="errorInPasswordFormat">密码最少%0%位，且至少包含%1%位非字母数字符号</key>
    <key alias="errorIntegerWithoutTab">%0% 必须是整数</key>
    <key alias="errorMandatory">%1% 中的 %0% 字段是必填项</key>
    <key alias="errorMandatoryWithoutTab">%0% 是必填项</key>
    <key alias="errorRegExp">%1% 中的 %0% 格式不正确</key>
    <key alias="errorRegExpWithoutTab">%0% 格式不正确</key>
  </area>
  <area alias="errors">
    <key alias="receivedErrorFromServer">从服务器收到错误</key>
    <key alias="dissallowedMediaType">该文件类型已被管理员禁用</key>
    <key alias="codemirroriewarning">注意，尽管配置中允许CodeMirror，但是它在IE上不够稳定，所以无法在IE运行。</key>
    <key alias="contentTypeAliasAndNameNotNull">请为新的属性类型填写名称和别名！</key>
    <key alias="filePermissionsError">权限有问题，访问指定文件或文件夹失败！</key>
    <key alias="macroErrorLoadingPartialView">加载Partial视图脚本时出错(文件: %0%)</key>
    <key alias="missingTitle">请输入标题</key>
    <key alias="missingType">请选择类型</key>
    <key alias="pictureResizeBiggerThanOrg">图片尺寸大于原始尺寸不会提高图片质量，您确定要把图片尺寸变大吗?</key>
    <key alias="startNodeDoesNotExists">默认打开页面不存在，请联系管理员</key>
    <key alias="stylesMustMarkBeforeSelect">请先选择内容，再设置样式。</key>
    <key alias="stylesNoStylesOnPage">没有可用的样式</key>
    <key alias="tableColMergeLeft">请把光标放在您要合并的两个单元格中的左边单元格</key>
    <key alias="tableSplitNotSplittable">非合并单元格不能分离。</key>
  </area>
  <area alias="general">
    <key alias="about">关于</key>
    <key alias="action">操作</key>
    <key alias="actions">操作</key>
    <key alias="add">添加</key>
    <key alias="alias">别名</key>
    <key alias="all">所有</key>
    <key alias="areyousure">您确定吗？</key>
    <key alias="back">返回</key>
    <key alias="border">边框</key>
    <key alias="by">或</key>
    <key alias="cancel">取消</key>
    <key alias="cellMargin">单元格边距</key>
    <key alias="choose">选择</key>
    <key alias="close">关闭</key>
    <key alias="closewindow">关闭窗口</key>
    <key alias="comment">备注</key>
    <key alias="confirm">确认</key>
    <key alias="constrainProportions">强制属性</key>
    <key alias="continue">继续</key>
    <key alias="copy">复制</key>
    <key alias="create">创建</key>
    <key alias="database">数据库</key>
    <key alias="date">时间</key>
    <key alias="default">默认</key>
    <key alias="delete">删除</key>
    <key alias="deleted">已删除</key>
    <key alias="deleting">正在删除…</key>
    <key alias="design">设计</key>
    <key alias="dimensions">规格</key>
    <key alias="down">下</key>
    <key alias="download">下载</key>
    <key alias="edit">编辑</key>
    <key alias="edited">已编辑</key>
    <key alias="elements">元素</key>
    <key alias="email">邮箱</key>
    <key alias="error">错误</key>
    <key alias="findDocument">查找文档</key>
    <key alias="height">高</key>
    <key alias="help">帮助</key>
    <key alias="icon">图标</key>
    <key alias="import">导入</key>
    <key alias="innerMargin">内边距</key>
    <key alias="insert">插入</key>
    <key alias="install">安装</key>
    <key alias="invalid">无效</key>
    <key alias="justify">对齐</key>
    <key alias="language">语言</key>
    <key alias="layout">布局</key>
    <key alias="loading">加载中</key>
    <key alias="locked">锁定</key>
    <key alias="login">登录</key>
    <key alias="logoff">退出</key>
    <key alias="logout">注销</key>
    <key alias="macro">宏</key>
    <key alias="mandatory">必填项</key>
    <key alias="move">移动</key>
    <key alias="name">名称</key>
    <key alias="new">新的</key>
    <key alias="next">下一步</key>
    <key alias="no">否</key>
    <key alias="of">属于</key>
    <key alias="ok">确定</key>
    <key alias="open">打开</key>
    <key alias="or">或</key>
    <key alias="password">密码</key>
    <key alias="path">路径</key>
    <key alias="pleasewait">请稍候…</key>
    <key alias="previous">上一步</key>
    <key alias="properties">属性</key>
    <key alias="reciept">接收数据邮箱</key>
    <key alias="recycleBin">回收站</key>
    <key alias="remaining">保持状态中</key>
    <key alias="rename">重命名</key>
    <key alias="renew">更新</key>
    <key alias="required" version="7.0">必填</key>
    <key alias="retry">重试</key>
    <key alias="rights">权限</key>
    <key alias="search">搜索</key>
    <key alias="server">服务器</key>
    <key alias="show">显示</key>
    <key alias="showPageOnSend">在发送时预览</key>
    <key alias="size">大小</key>
    <key alias="sort">排序</key>
    <key alias="submit">提交</key>
    <!-- TODO: Translate this -->
    <key alias="type">类型</key>
    <key alias="typeToSearch">输入内容开始查找…</key>
    <key alias="up">上</key>
    <key alias="update">更新</key>
    <key alias="upgrade">更新</key>
    <key alias="upload">上传</key>
    <key alias="url">链接地址</key>
    <key alias="user">用户</key>
    <key alias="username">用户名</key>
    <key alias="value">值</key>
    <key alias="view">查看</key>
    <key alias="welcome">欢迎…</key>
    <key alias="width">宽</key>
    <key alias="yes">是</key>
    <key alias="folder">文件夹</key>
    <key alias="searchResults">搜索结果</key>
    <key alias="reorder">重新排序</key>
    <key alias="reorderDone">我已结束排序</key>
    <key alias="preview">预览</key>
    <key alias="changePassword">更改密码</key>
    <key alias="to">至</key>
    <key alias="listView">列表视图</key>
    <key alias="saving">保存中...</key>
    <key alias="current">当前</key>
    <key alias="embed">嵌入</key>
    <key alias="selected">已选择</key>
  </area>
  <area alias="colors">
    <key alias="blue">蓝色</key>
  </area>
  <area alias="shortcuts">
    <key alias="addGroup">添加选项卡</key>
    <key alias="addProperty">添加属性</key>
    <key alias="addEditor">添加编辑器</key>
    <key alias="addTemplate">添加模板</key>
    <key alias="addChildNode">添加子节点</key>
    <key alias="addChild">添加子项</key>
    <key alias="editDataType">编辑数据类型</key>
    <key alias="navigateSections">导航节</key>
    <key alias="shortcut">快捷方式</key>
    <key alias="showShortcuts">显示快捷方式</key>
    <key alias="toggleListView">切换列表视图</key>
    <key alias="toggleAllowAsRoot">切换允许作为根</key>
  </area>
  <area alias="graphicheadline">
    <key alias="backgroundcolor">背景色</key>
    <key alias="bold">粗体</key>
    <key alias="color">前景色</key>
    <key alias="font">字体</key>
    <key alias="text">文本</key>
  </area>
  <area alias="headers">
    <key alias="page">页面</key>
  </area>
  <area alias="installer">
    <key alias="databaseErrorCannotConnect">无法连接到数据库。</key>
    <key alias="databaseFound">发现数据库</key>
    <key alias="databaseHeader">数据库配置</key>
    <key alias="databaseInstall"><![CDATA[
      点击<strong>安装</strong>进行 %0% 数据库配置
    ]]></key>
    <key alias="databaseInstallDone"><![CDATA[%0%数据库安装完成。点击<strong>下一步</strong>继续。]]></key>
    <key alias="databaseText"><![CDATA[完成本步，需要配置一个正确的连接字符串（“connection string”）。<br />
      如有必要，请联系您的系统管理员。
      如果您是本机安装，请使用管理员账号。]]></key>
    <key alias="databaseUpgrade"><![CDATA[
      <p>
     点击<strong>更新</strong>来更新系统到 %0%</p>
      <p>
      不用担心更新会丢失数据！
      </p>
    ]]></key>
    <key alias="databaseUpgradeDone"><![CDATA[数据库已更新到版本 %0%。<br />点击<strong>下一步</strong>继续。]]></key>
    <key alias="databaseUpToDate"><![CDATA[您的数据库已安装！点击<strong>下一步</strong>继续]]></key>
    <key alias="defaultUserChangePass"><![CDATA[<strong>需要修改默认密码！</strong>]]></key>
    <key alias="defaultUserDisabled"><![CDATA[<strong>默认账户已禁用或无权访问系统！</strong></p><p>点击<b>下一步</b>继续。]]></key>
    <key alias="defaultUserPassChanged"><![CDATA[<strong>安装过程中默认用户密码已更改</strong></p><p>点击<strong>下一步</strong>继续。]]></key>
    <key alias="defaultUserPasswordChanged">密码已更改</key>
    <key alias="greatStart">作为入门者，从视频教程开始吧！</key>
    <key alias="None">安装失败。</key>
    <key alias="permissionsAffectedFolders">受影响的文件和文件夹</key>
    <key alias="permissionsAffectedFoldersMoreInfo">此处查看更多信息</key>
    <key alias="permissionsAffectedFoldersText">您需要对以下文件和文件夹授于ASP.NET用户修改权限</key>
    <key alias="permissionsAlmostPerfect"><![CDATA[<strong>您当前的安全设置满足要求!</strong><br /><br />
        您可以毫无问题的运行系统，但您不能安装系统所推荐的扩展包的完整功能。]]></key>
    <key alias="permissionsHowtoResolve">如何解决</key>
    <key alias="permissionsHowtoResolveLink">点击阅读文字版</key>
    <key alias="permissionsHowtoResolveText"><![CDATA[观看我们的<strong>视频教程</strong> ]]></key>
    <key alias="permissionsMaybeAnIssue"><![CDATA[<strong>您当前的安全设置有问题！</strong>
      <br/><br />
      您可以毫无问题的运行系统，但您不能新建文件夹、也不能安装系统所推荐的包的完整功能。    ]]></key>
    <key alias="permissionsNotReady"><![CDATA[<strong>您当前的安全设置不适合于系统!</strong>
          <br /><br />
         您需要修改系统访问权限。]]></key>
    <key alias="permissionsPerfect"><![CDATA[<strong>您当前的权限设置正确！</strong><br /><br />
             您可以运行系统并安装其它扩展包！]]></key>
    <key alias="permissionsResolveFolderIssues">解决文件夹问题</key>
    <key alias="permissionsResolveFolderIssuesLink">点此查看ASP.NET和创建文件夹的问题解决方案</key>
    <key alias="permissionsSettingUpPermissions">设置文件夹权限</key>
    <key alias="permissionsText"><![CDATA[
      系统需要磁盘的读写权限以实现功能，
      比如模板文件、站点中Cache文件的的操作等。
    ]]></key>
    <key alias="runwayFromScratch">我要从头开始</key>
    <key alias="runwayFromScratchText"><![CDATA[
       此时您的网站是全空的,您应该首先建立您的文档类型和模板
        (<a href="https://umbraco.tv/documentation/videos/for-site-builders/foundation/document-types">如何操作？</a>)
       您也可以安装晚一些安装“Runway”。
    ]]></key>
    <key alias="runwayHeader">您刚刚安装了一个干净的系统，要继续吗？</key>
    <key alias="runwayInstalled">“Runway”已安装</key>
    <key alias="runwayInstalledText"><![CDATA[
                在顶部选择您想要的功能模块<br />
      这是我们推荐的模块，您也可以查看 <a href="#" onclick="toggleModules(); return false;" id="toggleModuleList">全部模块</a>
    ]]></key>
    <key alias="runwayOnlyProUsers">仅推荐高级用户使用</key>
    <key alias="runwaySimpleSite">给我一个简单的网站</key>
    <key alias="runwaySimpleSiteText"><![CDATA[
      <p>
      “Runway”是一个简单的，包含文件类型和模板的示例网站。安装程序会自动为您安装。
       您可以自行编辑和删除之。
        “Runway”为新手提供了最佳的入门功能

        </p>
        <small>
        <em>Runway:</em> 主页, 开始页, 安装模块页.<br />
        <em>可选模块:</em> 顶部导航, 站点地图, 联系我们, 图库.
        </small>
    ]]></key>
    <key alias="runwayWhatIsRunway">“Runway”是什么？</key>
    <key alias="step1">步骤 1/5：接受许可协议</key>
    <key alias="step2">步骤 2/5：数据库配置</key>
    <key alias="step3">步骤 3/5：文件权限验证</key>
    <key alias="step4">步骤 4/5：系统安全性</key>
    <key alias="step5">步骤 5/5：一切就绪，可以开始使用系统。</key>
    <key alias="thankYou">感谢选择我们的产品</key>
    <key alias="theEndBrowseSite"><![CDATA[<h3>浏览您的新站点</h3>
您安装了“Runway”，那么来瞧瞧吧。]]></key>
    <key alias="theEndFurtherHelp"><![CDATA[<h3>更多的帮助信息</h3>
从社区获取帮助]]></key>
    <key alias="theEndHeader">系统 %0% 安装完毕</key>
    <key alias="theEndInstallSuccess"><![CDATA[您想要<strong>立即开始</strong>请点“运行系统”<br />如果您是<strong>新手</strong>, 您可以得到相当丰富的学习资源。]]></key>
    <key alias="theEndOpenUmbraco"><![CDATA[<h3>运行系统</h3>
管理您的网站, 运行后台添加内容，
也可以添加模板和功能。]]></key>
    <key alias="Unavailable">无法连接到数据库。</key>
    <key alias="Version3">系统版本 3</key>
    <key alias="Version4">系统版本 4</key>
    <key alias="watch">观看</key>
    <key alias="welcomeIntro"><![CDATA[本向导将指引您完成配置和安装（或升级安装）系统
<br /><br />
按 <strong>“下一步”</strong>进入向导。]]></key>
  </area>
  <area alias="language">
    <key alias="cultureCode">语言代码</key>
    <key alias="displayName">语言名称</key>
  </area>
  <area alias="lockout">
    <key alias="lockoutWillOccur">用户在空闲状态下将会自动注销</key>
    <key alias="renewSession">已更新，继续工作。</key>
  </area>
  <area alias="login">
    <key alias="greeting0">星期一快乐</key>
    <key alias="greeting1">星期二快乐</key>
    <key alias="greeting2">星期三快乐</key>
    <key alias="greeting3">星期四快乐</key>
    <key alias="greeting4">星期五快乐</key>
    <key alias="greeting5">星期六快乐</key>
    <key alias="greeting6">星期天快乐</key>
    <key alias="instruction">在下方登录</key>
    <key alias="signInWith">登录</key>
    <key alias="timeout">会话超时</key>
    <key alias="bottomText"><![CDATA[<p style="text-align:right;">&copy; 2001 - %0% <br /><a href="https://umbraco.com" style="text-decoration: none" target="_blank" rel="noopener">Umbraco.com</a></p> ]]></key>
    <key alias="forgottenPassword">忘记密码?</key>
    <key alias="forgottenPasswordInstruction">电子邮件将发送到地址指定的链接, 以重置您的密码</key>
    <key alias="requestPasswordResetConfirmation">如果电子邮件与我们的记录相符, 则将发送带有密码重置指令的邮件</key>
    <key alias="returnToLogin">返回登录表单</key>
    <key alias="setPasswordInstruction">请提供新密码</key>
    <key alias="setPasswordConfirmation">您的密码已更新</key>
    <key alias="resetCodeExpired">您单击的链接无效或已过期</key>
    <key alias="resetPasswordEmailCopySubject">Umbraco: 重置密码</key>
    <key alias="resetPasswordEmailCopyFormat"><![CDATA[<p>您的用户名登录到 Umbraco 后台是: <strong>%0%</strong></p><p>点击 <a href="%1%"><strong>这里</strong></a> 重置密码，或复制链接粘贴到您的浏览器访问:</p><p><em>%1%</em></p>]]></key>
  </area>
  <area alias="main">
    <key alias="dashboard">仪表板</key>
    <key alias="sections">区域</key>
    <key alias="tree">内容</key>
  </area>
  <area alias="moveOrCopy">
    <key alias="choose">选择上面的页面…</key>
    <key alias="copyDone">%0% 被复制到 %1%</key>
    <key alias="copyTo">将 %0% 复制到</key>
    <key alias="moveDone">%0% 已被移动到 %1%</key>
    <key alias="moveTo">将 %0% 移动到</key>
    <key alias="nodeSelected">作为内容的根结点，点“确定”。</key>
    <key alias="noNodeSelected">尚未选择节点，请选择一个节点点击“确定”。</key>
    <key alias="notAllowedByContentType">类型不符不允许选择</key>
    <key alias="notAllowedByPath">该项不能移到其子项</key>
    <key alias="notAllowedAtRoot">当前节点不能建在根节点下</key>
    <key alias="notValid">您在子项的权限不够，不允许该操作。</key>
    <key alias="relateToOriginal">复本和原本建立关联</key>
  </area>
  <area alias="notifications">
    <key alias="editNotifications">为 %0% 编写通知</key>
    <key alias="mailBody"><![CDATA[
%0%：

  您好！这是一封自动邮件，提醒您用户'%3%'
  执行'%1%'任务
  已经在完成'%2%'。

      转到 http://%4%/#/content/content/edit/%5% 进行编辑

      Have a nice day!

      来自Umbraco机器人
    ]]></key>
    <key alias="mailBodyHtml"><![CDATA[<p>%0%：</p>

		  <p>您好！这是一封自动发送的邮件，告诉您任务<strong>'%1%'</strong>
		  已在<a href="http://%4%/actions/preview.aspx?id=%5%"><strong>'%2%'</strong></a>
		  被用户<strong>'%3%'</strong>执行
		  </p>
		  <div style="margin: 8px 0; padding: 8px; display: block;">
				<br />
				<a style="color: white; font-weight: bold; background-color: #5372c3; text-decoration : none; margin-right: 20px; border: 8px solid #5372c3; width: 150px;" href="http://%4%/#/content/content/edit/%5%">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;编辑&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</a> &nbsp;
				<br />
		  </div>
		  <p>
                          <h3>更新概况：</h3>
			  <table style="width: 100%;">
						   %6%
				</table>
			 </p>

		  <div style="margin: 8px 0; padding: 8px; display: block;">
				<br />
                                <a style="color: white; font-weight: bold; background-color: #5372c3; text-decoration : none; margin-right: 20px; border: 8px solid #5372c3; width: 150px;" href="http://%4%/#/content/content/edit/%5%">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;编辑&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</a> &nbsp;
                                <br />
                  </div>

                  <p>祝您愉快！<br /><br />
                          该信息由系统自动发送
                  </p>]]></key>
    <key alias="mailSubject">在 %2%，[%0%] 关于 %1% 的通告已执行。</key>
    <key alias="notifications">通知</key>
  </area>
  <area alias="packager">
    <key alias="chooseLocalPackageText"><![CDATA[
      从本机安装扩展包，可以点击“浏览”按钮<br />
      选择 ".umb" 或者 ".zip" 文件
    ]]></key>
    <key alias="packageAuthor">作者</key>
    <key alias="packageDocumentation">文档</key>
    <key alias="packageMetaData">元数据</key>
    <key alias="packageName">名称</key>
    <key alias="packageNoItemsHeader">扩展包不含任何项</key>
    <key alias="packageNoItemsText"><![CDATA[该扩展包不包含任何项<br/><br/>
      点击下面的“卸载”，您可以安全的删除。]]></key>
    <key alias="packageOptions">选项</key>
    <key alias="packageReadme">说明</key>
    <key alias="packageRepository">程序库</key>
    <key alias="packageUninstallConfirm">确认卸载</key>
    <key alias="packageUninstalledHeader">已卸载</key>
    <key alias="packageUninstalledText">扩展包卸载成功</key>
    <key alias="packageUninstallHeader">卸载</key>
    <key alias="packageUninstallText"><![CDATA[选择想要卸载的项目，点击“卸载”<br />
      <span style="color: Red; font-weight: bold;">注意：</span>
      卸载包将导致所有依赖该包的东西失效，请确认。    ]]></key>
    <key alias="packageVersion">版本</key>
  </area>
  <area alias="paste">
    <key alias="doNothing">带格式粘贴（不推荐）</key>
    <key alias="errorMessage">您所粘贴的文本含有特殊字符或格式，Umbraco将清除以适应网页。</key>
    <key alias="removeAll">无格式粘贴</key>
    <key alias="removeSpecialFormattering">粘贴并移除格式（推荐）</key>
  </area>
  <area alias="publicAccess">
    <key alias="paAdvanced">基于角色的保护</key>
    <key alias="paAdvancedHelp"><![CDATA[基于角色的授权，<br /> 要使用的会员组。]]></key>
    <key alias="paAdvancedNoGroups">使用基于角色的授权需要首先建立会员组。</key>
    <key alias="paErrorPage">错误页</key>
    <key alias="paErrorPageHelp">当用户登录后访问没有权限的页时显示该页</key>
    <key alias="paHowWould">选择限制访问此页的方式</key>
    <key alias="paIsProtected">%0% 现在处于受保护状态</key>
    <key alias="paIsRemoved">%0% 的保护被取消 </key>
    <key alias="paLoginPage">登录页</key>
    <key alias="paLoginPageHelp">选择公开的登录入口</key>
    <key alias="paRemoveProtection">取消保护</key>
    <key alias="paSelectPages">选择一个包含登录表单和提示信息的页</key>
    <key alias="paSelectRoles">选择访问该页的角色类型</key>
    <key alias="paSetLogin">为此页设置账号和密码</key>
    <key alias="paSimple">单用户保护</key>
    <key alias="paSimpleHelp">如果您只希望提供一个用户名和密码就能访问</key>
  </area>
  <area alias="publish">
    <key alias="contentPublishedFailedAwaitingRelease"><![CDATA[
      %0% 无法发布, 因为该项在计划发布中。
    ]]></key>
    <key alias="contentPublishedFailedExpired"><![CDATA[
      %0% 无法发布, 因为该项已过期。
    ]]></key>
    <key alias="contentPublishedFailedInvalid"><![CDATA[
       %0%不能发布，因为%1%字段不合验证规则。
    ]]></key>
    <key alias="contentPublishedFailedByEvent"><![CDATA[
      %0% 无法发布，第三方组件造成失败。
    ]]></key>
    <key alias="contentPublishedFailedByParent"><![CDATA[
      %0% 不能发布，因为上级页面没有发布。
    ]]></key>
    <key alias="includeUnpublished">包含未发布的子项</key>
    <key alias="inProgress">正在发布，请稍候…</key>
    <key alias="inProgressCounter">%0% 中的 %1% 页面已发布…</key>
    <key alias="nodePublish">%0% 已发布</key>
    <key alias="nodePublishAll">%0% 及其子项已发布</key>
    <key alias="publishAll">发布 %0% 及其子项</key>
    <key alias="publishHelp"><![CDATA[点 <em>确定</em> 发布 <strong>%0%</strong><br/><br />
       要发布当前页和所有子页，请选中 <em>全部发布</em> 发布所有子页。
     ]]></key>
  </area>
  <area alias="colorpicker">
    <key alias="noColors">您没有配置任何认可的颜色</key>
  </area>
  <area alias="relatedlinks">
    <key alias="enterExternal">输入外部链接</key>
    <key alias="chooseInternal">选择内部页面</key>
    <key alias="caption">标题</key>
    <key alias="link">链接</key>
    <key alias="newWindow">新窗口</key>
    <key alias="captionPlaceholder">输入新标题</key>
    <key alias="externalLinkPlaceholder">输入链接</key>
  </area>
  <area alias="imagecropper">
    <key alias="reset">Reset</key>
  </area>
  <area alias="rollback">
    <key alias="currentVersion">当前版本</key>
    <key alias="diffHelp"><![CDATA[显示当前版本和选择版本的差异<br /><del>红色</del>是选中版本中没有的。<ins>绿色是新增的</ins>]]></key>
    <key alias="documentRolledBack">文档已回滚</key>
    <key alias="htmlHelp"><![CDATA[将选中版本显示为HTML，如果您想看到版本间的差异比较，请使用对比视图。]]></key>
    <key alias="rollbackTo">回滚至</key>
    <key alias="selectVersion">选择版本</key>
    <key alias="view">查看</key>
  </area>
  <area alias="scripts">
    <key alias="editscript">编辑脚本</key>
  </area>
  <area alias="sections">
    <key alias="concierge">礼宾</key>
    <key alias="content">内容</key>
    <key alias="courier">导游</key>
    <key alias="developer">开发</key>
    <key alias="installer">Umbraco配置向导</key>
    <key alias="media">媒体</key>
    <key alias="member">会员</key>
    <key alias="newsletters">消息</key>
    <key alias="settings">设置</key>
    <key alias="statistics">统计</key>
    <key alias="translation">翻译</key>
    <key alias="users">用户</key>
    <key alias="help" version="7.0">帮助</key>
    <key alias="forms">窗体</key>
  </area>
  <area alias="help">
    <key alias="theBestUmbracoVideoTutorials">最佳 Umbraco 视频教程</key>
  </area>
  <area alias="settings">
    <key alias="defaulttemplate">默认模板</key>
    <key alias="importDocumentTypeHelp">要导入文档类型，请点击“浏览”按钮，再点击“导入”，然后在您电脑上查找 ".udt"文件导入（下一页中需要您再次确认）</key>
    <key alias="newtabname">新建选项卡标题</key>
    <key alias="nodetype">节点类型</key>
    <key alias="objecttype">类型</key>
    <key alias="stylesheet">样式表</key>
    <key alias="script">脚本</key>
    <key alias="tab">选项卡</key>
    <key alias="tabname">选项卡标题</key>
    <key alias="tabs">选项卡</key>
    <key alias="contentTypeEnabled">主控文档类型激活</key>
    <key alias="contentTypeUses">该文档类型使用</key>
    <key alias="noPropertiesDefinedOnTab">没有字段设置在该标签页</key>
    <key alias="addIcon">添加图标</key>
  </area>
  <area alias="sort">
    <key alias="sortOrder">排序次序</key>
    <key alias="sortCreationDate">创建日期</key>
    <key alias="sortDone">排序完成。</key>
    <key alias="sortHelp">上下拖拽项目或单击列头进行排序</key>
    <key alias="sortPleaseWait"><![CDATA[正在排序请稍候…]]></key>
  </area>
  <area alias="speechBubbles">
    <key alias="validationFailedHeader">验证</key>
    <key alias="validationFailedMessage">在保存项之前必须修复验证错误</key>
    <key alias="operationFailedHeader">失败</key>
    <key alias="invalidUserPermissionsText">用户权限不足, 无法完成操作</key>
    <key alias="operationCancelledHeader">取消</key>
    <key alias="operationCancelledText">操作被第三方插件取消。</key>
    <key alias="contentPublishedFailedByEvent">发布因为第三方插件取消</key>
    <key alias="contentTypeDublicatePropertyType">属性类型已存在</key>
    <key alias="contentTypePropertyTypeCreated">属性类型已创建</key>
    <key alias="contentTypePropertyTypeCreatedText"><![CDATA[名称：%0% <br />数据类型：%1%]]></key>
    <key alias="contentTypePropertyTypeDeleted">属性类型已删除</key>
    <key alias="contentTypeSavedHeader">内容类型已保存</key>
    <key alias="contentTypeTabCreated">选项卡已创建</key>
    <key alias="contentTypeTabDeleted">选项卡已删除</key>
    <key alias="contentTypeTabDeletedText">id为%0%的选项卡已删除</key>
    <key alias="cssErrorHeader">样式表未保存</key>
    <key alias="cssSavedHeader">样式表已保存</key>
    <key alias="cssSavedText">样式表保存，无错误。</key>
    <key alias="dataTypeSaved">数据类型已保存</key>
    <key alias="dictionaryItemSaved">字典项已保存</key>
    <key alias="editContentPublishedFailedByParent">因为上级页面未发布导致发布失败！</key>
    <key alias="editContentPublishedHeader">内容已发布</key>
    <key alias="editContentPublishedText">公众可见</key>
    <key alias="editContentSavedHeader">内容已保存</key>
    <key alias="editContentSavedText">请发布以使更改生效</key>
    <key alias="editContentSendToPublish">提交审核</key>
    <key alias="editContentSendToPublishText">更改已提交审核</key>
    <key alias="editMediaSaved">媒体已保存</key>
    <key alias="editMediaSavedText">媒体已保存</key>
    <key alias="editMemberSaved">会员已保存</key>
    <key alias="editStylesheetPropertySaved">样式表属性已保存</key>
    <key alias="editStylesheetSaved">样式表已保存</key>
    <key alias="editTemplateSaved">模板已保存</key>
    <key alias="editUserError">保存用户出错（请查看日志）</key>
    <key alias="editUserSaved">用户已保存</key>
    <key alias="editUserTypeSaved">用户类型已保存</key>
    <key alias="fileErrorHeader">文件未保存</key>
    <key alias="fileErrorText">文件无法保存，请检查权限。</key>
    <key alias="fileSavedHeader">文件保存</key>
    <key alias="fileSavedText">文件保存，无错误。</key>
    <key alias="languageSaved">语言已保存</key>
    <key alias="mediaTypeSavedHeader">已保存媒体类型</key>
    <key alias="memberTypeSavedHeader">已保存成员类型</key>
    <key alias="templateErrorHeader">模板未保存</key>
    <key alias="templateErrorText">模板别名相同</key>
    <key alias="templateSavedHeader">模板已保存</key>
    <key alias="templateSavedText">模板保存，无错误。</key>
    <key alias="contentUnpublished">未发布内容</key>
    <key alias="partialViewSavedHeader">片段视图已保存</key>
    <key alias="partialViewSavedText">片段视图保存，无错误。</key>
    <key alias="partialViewErrorHeader">片段视图未保存</key>
    <key alias="partialViewErrorText">片段视图因为错误未能保存</key>
  </area>
  <area alias="stylesheet">
    <key alias="aliasHelp">使用CSS语法，如：h1、.redHeader、.blueTex。</key>
    <key alias="editstylesheet">编辑样式表</key>
    <key alias="editstylesheetproperty">编辑样式属性</key>
    <key alias="nameHelp">编辑器中的样式属性名 </key>
    <key alias="preview">预览</key>
    <key alias="styles">样式</key>
  </area>
  <area alias="template">
    <key alias="edittemplate">编辑模板</key>
    <key alias="insertContentArea">插入内容区</key>
    <key alias="insertContentAreaPlaceHolder">插入内容占位符</key>
    <key alias="insertDictionaryItem">插入字典项</key>
    <key alias="insertMacro">插入宏</key>
    <key alias="insertPageField">插入页字段</key>
    <key alias="mastertemplate">母版</key>
    <key alias="quickGuide">模板标签快速指南</key>
    <key alias="template">模板</key>
  </area>
  <area alias="grid">
    <key alias="media">Image</key>
    <key alias="macro">Macro</key>
    <key alias="insertControl">选择内容类别</key>
    <key alias="chooseLayout">选择一项布局</key>
    <key alias="addRows">添加一行</key>
    <key alias="addElement">添加内容</key>
    <key alias="dropElement">丢弃内容</key>
    <key alias="settingsApplied">设置已应用</key>
    <key alias="contentNotAllowed">此处不允许有该内容</key>
    <key alias="contentAllowed">此处允许有该内容</key>
    <key alias="clickToEmbed">点击嵌入</key>
    <key alias="clickToInsertImage">点击添加图片</key>
    <key alias="placeholderWriteHere">在这里输入...</key>
    <key alias="gridLayouts">网格布局</key>
    <key alias="gridLayoutsDetail">布局是网格编辑器的整体工作区域, 通常只需要一个或两个不同的布局</key>
    <key alias="addGridLayout">添加网络布局</key>
    <key alias="addGridLayoutDetail">通过设置列宽并添加其他节来调整版式</key>
    <key alias="rowConfigurations">行配置</key>
    <key alias="rowConfigurationsDetail">行是水平排列的预定义单元格</key>
    <key alias="addRowConfiguration">添加行配置</key>
    <key alias="addRowConfigurationDetail">通过设置单元格宽度和添加其他单元格来调整行</key>
    <key alias="columns">列</key>
    <key alias="columnsDetails">网格布局中的总和列数</key>
    <key alias="settings">设置</key>
    <key alias="settingsDetails">配置编辑器可以更改的设置</key>
    <key alias="styles">样式</key>
    <key alias="stylesDetails">配置编辑器可以更改的样式</key>
    <key alias="allowAllEditors">允许所有的编辑器</key>
    <key alias="allowAllRowConfigurations">允许所有行配置</key>
    <key alias="setAsDefault">设置为默认值</key>
    <key alias="chooseExtra">选择附加</key>
    <key alias="chooseDefault">选择默认值</key>
    <key alias="areAdded">已增加</key>
  </area>
  <area alias="contentTypeEditor">
    <key alias="compositions">组合</key>
    <key alias="noGroups">您没有添加任何选项卡</key>
    <key alias="inheritedFrom">继承自</key>
    <key alias="addProperty">添加属性</key>
    <key alias="requiredLabel">必需的标签</key>
    <key alias="enableListViewHeading">启用列表视图</key>
    <key alias="enableListViewDescription">配置内容项以显示其子项的可排序和搜索列表, 这些子项将不会显示在树中</key>
    <key alias="allowedTemplatesHeading">允许的模板</key>
    <key alias="allowedTemplatesDescription">选择允许在该类型的内容上使用哪些模板编辑器</key>
    <key alias="allowAsRootHeading">允许作为根</key>
    <key alias="allowAsRootDescription">允许编辑器在内容树的根目录中创建此类型的内容</key>
    <key alias="childNodesHeading">允许的子节点类型</key>
    <key alias="childNodesDescription">允许在该类型的内容下方创建指定类型的内容</key>
    <key alias="chooseChildNode">选择子节点</key>
    <key alias="compositionsDescription">从现有文档类型继承选项卡和属性。如果存在同名的选项卡, 则新选项卡将添加到当前文档类型或合并。</key>
    <key alias="compositionInUse">此内容类型在组合中使用, 因此不能自行组成。</key>
    <key alias="noAvailableCompositions">没有可供组合使用的内容类型。</key>
    <key alias="availableEditors">可用编辑器</key>
    <key alias="reuse">重用</key>
    <key alias="editorSettings">编辑器设置</key>
    <key alias="configuration">配置</key>
    <key alias="yesDelete">是，删除</key>
    <key alias="movedUnderneath">被移动到下方</key>
    <key alias="copiedUnderneath">被复制到下面</key>
    <key alias="folderToMove">选择要移动的文件夹</key>
    <key alias="folderToCopy">选择要复制的文件夹</key>
    <key alias="structureBelow">在下面的树结构中</key>
    <key alias="allDocumentTypes">所有文档类型</key>
    <key alias="allDocuments">所有文档</key>
    <key alias="allMediaItems">所有媒体项目</key>
    <key alias="usingThisDocument">使用此文档类型将被永久删除, 请确认您还要删除这些文件。</key>
    <key alias="usingThisMedia">使用此媒体类型将被永久删除, 请确认您也要删除这些。</key>
    <key alias="usingThisMember">使用此成员类型将被永久删除, 请确认您想要删除这些</key>
    <key alias="andAllDocuments">和所有使用此类型的文档</key>
    <key alias="andAllMediaItems">和所有使用此类型的媒体项目</key>
    <key alias="andAllMembers">和使用此类型的所有成员</key>
    <key alias="memberCanEdit">成员可编辑</key>
    <key alias="showOnMemberProfile">显示成员配置文件</key>
  </area>
  <area alias="templateEditor">
    <key alias="alternativeField">替代字段</key>
    <key alias="alternativeText">替代文本</key>
    <key alias="casing">大小写</key>
    <key alias="chooseField">选取字段</key>
    <key alias="convertLineBreaks">转换换行符</key>
    <key alias="convertLineBreaksHelp">将换行符转化为&amp;lt;br&amp;gt;</key>
    <key alias="customFields">自定义字段</key>
    <key alias="dateOnly">是，仅日期</key>
    <key alias="encoding">编码</key>
    <key alias="formatAsDate">格式化时间</key>
    <key alias="htmlEncode">HTML编码</key>
    <key alias="htmlEncodeHelp">将替换HTML中的特殊字符</key>
    <key alias="insertedAfter">将在字段值后插入</key>
    <key alias="insertedBefore">将在字段值前插入</key>
    <key alias="lowercase">小写</key>
    <key alias="none">无</key>
    <key alias="postContent">字段后插入</key>
    <key alias="preContent">字段前插入</key>
    <key alias="recursive">递归</key>
    <key alias="removeParagraph">移除段落符号</key>
    <key alias="removeParagraphHelp">将移除&amp;lt;P&amp;gt;标签</key>
    <key alias="standardFields">标准字段</key>
    <key alias="uppercase">大写</key>
    <key alias="urlEncode">URL编码</key>
    <key alias="urlEncodeHelp">将格式化URL中的特殊字符</key>
    <key alias="usedIfAllEmpty">当上面字段值为空时使用</key>
    <key alias="usedIfEmpty">该字段仅在主字段为空时使用</key>
    <key alias="withTime">是，含时间，分隔符为： </key>
  </area>
  <area alias="translation">
    <key alias="details">翻译详情</key>
    <key alias="DownloadXmlDTD">下载 XML DTD</key>
    <key alias="fields">字段</key>
    <key alias="includeSubpages">包含子页</key>
    <key alias="mailBody"><![CDATA[
     %0%：

     您好！这是一封自动邮件来提醒您注意，%2%的文档'%1%'
     需要您翻译为'%5%'

     转到 http://%3%/translation/details.aspx?id=%4% 进行编辑

      或登录下列网址查看翻译任务
      http://%3%

      Have a nice day!

      来自Umbraco 机器人的祝福
    ]]></key>
    <key alias="noTranslators">没有翻译员，请创建翻译员角色的用户。</key>
    <key alias="pageHasBeenSendToTranslation">页面'%0%'已经发送给翻译</key>
    <key alias="sendToTranslate">发送页面'%0%'以便翻译</key>
    <key alias="totalWords">总字数</key>
    <key alias="translateTo">翻译到</key>
    <key alias="translationDone">翻译完成。</key>
    <key alias="translationDoneHelp">您可以浏览刚翻译的页面，如果原始页存在，您将得到两者的比较。</key>
    <key alias="translationFailed">翻译失败，XML可能损坏了。</key>
    <key alias="translationOptions">翻译选项</key>
    <key alias="translator">翻译员</key>
    <key alias="uploadTranslationXml">上传翻译的xml</key>
  </area>
  <area alias="treeHeaders">
    <key alias="cacheBrowser">缓存浏览</key>
    <key alias="contentRecycleBin">回收站</key>
    <key alias="createdPackages">创建扩展包</key>
    <key alias="dataTypes">数据类型</key>
    <key alias="dictionary">字典</key>
    <key alias="installedPackages">已安装的扩展包</key>
    <key alias="installSkin">安装皮肤</key>
    <key alias="installStarterKit">安装新手套件</key>
    <key alias="languages">语言</key>
    <key alias="localPackage">安装本地扩展包</key>
    <key alias="macros">宏</key>
    <key alias="mediaTypes">媒体类型</key>
    <key alias="member">会员</key>
    <key alias="memberGroups">会员组</key>
    <key alias="memberRoles">角色</key>
    <key alias="memberTypes">会员类型</key>
    <key alias="documentTypes">文档类型</key>
    <key alias="relationTypes">关系类型</key>
    <key alias="packager">扩展包</key>
    <key alias="packages">扩展包</key>
    <key alias="repositories">从在线程序库安装</key>
    <key alias="runway">安装Runway</key>
    <key alias="runwayModules">Runway模块</key>
    <key alias="scripting">Scripting文件</key>
    <key alias="scripts">脚本</key>
    <key alias="stylesheets">样式表</key>
    <key alias="templates">模板</key>
    <key alias="userPermissions">用户权限</key>
    <key alias="users">Users</key>
    <key alias="partialViews">分部视图</key>
    <key alias="partialViewMacros">分部视图宏文件</key>
  </area>
  <area alias="update">
    <key alias="updateAvailable">有可用更新</key>
    <key alias="updateDownloadText">%0%已就绪，点击这里下载</key>
    <key alias="updateNoServer">无到服务器的连接</key>
    <key alias="updateNoServerError">检查更新失败</key>
  </area>
  <area alias="user">
    <key alias="administrators">管理员</key>
    <key alias="categoryField">分类字段</key>
    <key alias="changePassword">更改密码</key>
    <key alias="newPassword">更改密码</key>
    <key alias="confirmNewPassword">确认新密码</key>
    <key alias="changePasswordDescription">要改变密码，请在框中输入新密码，然后单击“更改密码”。</key>
    <key alias="contentChannel">内容频道</key>
    <key alias="descriptionField">描述字段</key>
    <key alias="disabled">禁用用户</key>
    <key alias="documentType">文档类型</key>
    <key alias="editors">编辑</key>
    <key alias="excerptField">排除字段</key>
    <key alias="language">语言</key>
    <key alias="loginname">登录</key>
    <key alias="mediastartnode">默认打开媒体项</key>
    <key alias="modules">区域</key>
    <key alias="noConsole">禁用后台管理界面</key>
    <key alias="oldPassword">旧密码</key>
    <key alias="password">密码</key>
    <key alias="resetPassword">重设密码</key>
    <key alias="passwordChanged">您的密码已更改！</key>
    <key alias="passwordConfirm">重输密码</key>
    <key alias="passwordEnterNew">输入新密码</key>
    <key alias="passwordIsBlank">新密码不能为空！</key>
    <key alias="passwordCurrent">当前密码</key>
    <key alias="passwordInvalid">密码错误</key>
    <key alias="passwordIsDifferent">新密码和重输入的密码不一致，请重试！</key>
    <key alias="passwordMismatch">重输的密码和原密码不一致！</key>
    <key alias="permissionReplaceChildren">替换子项权限设置</key>
    <key alias="permissionSelectedPages">您正在修改访问权限的页面：</key>
    <key alias="permissionSelectPages">选择要修改权限的页</key>
    <key alias="searchAllChildren">搜索子对象</key>
    <key alias="startnode">默认打开内容项</key>
    <key alias="username">用户名</key>
    <key alias="userPermissions">用户权限</key>
    <key alias="usertype">用户类型</key>
    <key alias="userTypes">用户类型</key>
    <key alias="writer">撰稿人</key>
    <key alias="change">更改</key>
    <key alias="yourProfile" version="7.0">你的资料</key>
    <key alias="yourHistory" version="7.0">你最近的历史信息</key>
    <key alias="sessionExpires" version="7.0">会话过期于</key>
  </area>
  <area alias="validation">
    <key alias="validation">验证</key>
    <key alias="validateAsEmail">验证为电子邮件</key>
    <key alias="validateAsNumber">验证为数字</key>
    <key alias="validateAsUrl">验证为 URL</key>
    <key alias="enterCustomValidation">...或输入自定义验证</key>
    <key alias="fieldIsMandatory">字段是强制性的</key>
  </area>
  <area alias="healthcheck">
    <!-- The following keys get these tokens passed in:
	     0: Current value
		   1: Recommended value
		   2: XPath
		   3: Configuration file path
	  -->
    <key alias="checkSuccessMessage">Value is set to the recommended value: '%0%'.</key>
    <key alias="checkErrorMessageDifferentExpectedValue">Expected value '%1%' for '%2%' in configuration file '%3%', but found '%0%'.</key>
    <key alias="checkErrorMessageUnexpectedValue">Found unexpected value '%0%' for '%2%' in configuration file '%3%'.</key>
    <!-- The following keys get these tokens passed in:
	     0: Current value
		   1: Recommended value
	  -->
    <key alias="macroErrorModeCheckSuccessMessage">MacroErrors are set to '%0%'.</key>
    <key alias="macroErrorModeCheckErrorMessage">MacroErrors are set to '%0%' which will prevent some or all pages in your site from loading completely if there are any errors in macros. Rectifying this will set the value to '%1%'.</key>
    <!-- The following keys get these tokens passed in:
	     0: Current value
		   1: Recommended value
		   2: Server version
	  -->
    <!-- The following keys get predefined tokens passed in that are not all the same, like above -->
    <key alias="httpsCheckValidCertificate">Your site certificate was marked as valid.</key>
    <key alias="httpsCheckInvalidCertificate">Certificate validation error: '%0%'</key>
    <key alias="healthCheckInvalidUrl">Error pinging the URL %0% - '%1%'</key>
    <key alias="httpsCheckIsCurrentSchemeHttps">You are currently %0% viewing the site using the HTTPS scheme.</key>
    <!-- The following keys don't get tokens passed in -->
    <key alias="compilationDebugCheckSuccessMessage">Debug compilation mode is disabled.</key>
    <key alias="compilationDebugCheckErrorMessage">Debug compilation mode is currently enabled. It is recommended to disable this setting before go live.</key>
    <!-- The following keys get these tokens passed in:
	    0: Comma delimitted list of failed folder paths
  	-->
    <!-- The following keys get these tokens passed in:
	    0: Path to the file not found
  	-->
    <!-- The following keys get these tokens passed in:
	    0: Comma delimitted list of failed folder paths
  	-->
    <key alias="clickJackingCheckHeaderFound"><![CDATA[The header or meta-tag <strong>X-Frame-Options</strong> used to control whether a site can be IFRAMEd by another was found.]]></key>
    <key alias="clickJackingCheckHeaderNotFound"><![CDATA[The header or meta-tag <strong>X-Frame-Options</strong> used to control whether a site can be IFRAMEd by another was not found.]]></key>
    <!-- The following key get these tokens passed in:
	    0: Comma delimitted list of headers found
  	-->
    <key alias="excessiveHeadersFound"><![CDATA[The following headers revealing information about the website technology were found: <strong>%0%</strong>.]]></key>
    <key alias="excessiveHeadersNotFound">No headers revealing information about the website technology were found.</key>
    <key alias="smtpMailSettingsConnectionSuccess">SMTP settings are configured correctly and the service is operating as expected.</key>
    <key alias="notificationEmailsCheckSuccessMessage"><![CDATA[Notification email has been set to <strong>%0%</strong>.]]></key>
    <key alias="notificationEmailsCheckErrorMessage"><![CDATA[Notification email is still set to the default value of <strong>%0%</strong>.]]></key>
  </area>
  <area alias="redirectUrls">
    <key alias="disableUrlTracker">禁用 URL 跟踪程序</key>
    <key alias="enableUrlTracker">启用 URL 跟踪程序</key>
    <key alias="originalUrl">原始网址</key>
    <key alias="redirectedTo">已重定向至</key>
    <key alias="noRedirects">未进行重定向</key>
    <key alias="noRedirectsDescription">当已发布的页重命名或移动时, 将自动对新页进行重定向。</key>
    <key alias="redirectRemoved">重定向URL已删除。</key>
    <key alias="redirectRemoveError">删除重定向 URL 时出错.</key>
    <key alias="confirmDisable">是否确实要禁用 URL 跟踪程序?</key>
    <key alias="disabledConfirm">URL 跟踪器现在已被禁用。</key>
    <key alias="disableError">禁用 URL 跟踪程序时出错, 可以在日志文件中找到更多信息。</key>
    <key alias="enabledConfirm">现在已启用 URL 跟踪程序。</key>
    <key alias="enableError">启用 URL 跟踪程序时出错, 可以在日志文件中找到更多信息。</key>
  </area>
  <area alias="logViewer">
    <key alias="selectAllLogLevelFilters">全选</key>
    <key alias="deselectAllLogLevelFilters">取消全选</key>
  </area>
</language>
