@echo off
REM Umbraco Log Cleanup Script (Simple version)
REM This script deletes log files older than 7 days

echo Umbraco Log Cleanup Script
echo =========================
echo.

if not exist "umbraco\Logs" (
    echo Error: umbraco\Logs directory not found!
    pause
    exit /b 1
)

echo Deleting log files older than 7 days...
echo.

REM Delete files older than 7 days
forfiles /p "umbraco\Logs" /m "*.json" /d -7 /c "cmd /c echo Deleting @file && del @path" 2>nul

if %errorlevel% equ 0 (
    echo.
    echo Cleanup completed successfully!
) else (
    echo.
    echo No old log files found or cleanup completed.
)

echo.
echo For more advanced options, use: powershell -ExecutionPolicy Bypass -File cleanup-logs.ps1
echo.
pause
