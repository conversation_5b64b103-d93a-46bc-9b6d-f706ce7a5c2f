<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net6.0</TargetFramework>
        <GenerateStaticWebAssetsManifest>false</GenerateStaticWebAssetsManifest>
    </PropertyGroup>

    <ItemGroup>
      <None Remove="EmailTemplates\Website\contactform.html" />
      <None Remove="error.html" />
      <None Remove="Page404.html" />
      <Content Include="Page404.html">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
      <Content Include="EmailTemplates\Website\contactform.html">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.10">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Serilog.Formatting.Compact" Version="3.0.0" />
        <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
        <PackageReference Include="Umbraco.Cms" Version="10.4.0" />
    </ItemGroup>

    <!-- Force Windows to use ICU. Otherwise Windows 10 2019H1+ will do it, but older Windows 10 and most, if not all, Windows Server editions will run NLS -->
    <ItemGroup>
      <PackageReference Include="Microsoft.ICU.ICU4C.Runtime" Version="********" />
      <PackageReference Include="Umbraco.StorageProviders.AzureBlob" Version="10.0.0" />
      <PackageReference Include="uSkinnedSiteBuilder" Version="3.0.5" />
      <RuntimeHostConfigurationOption Include="System.Globalization.AppLocalIcu" Value="********" Condition="$(RuntimeIdentifier.StartsWith('linux')) Or $(RuntimeIdentifier.StartsWith('win')) Or ('$(RuntimeIdentifier)' == '' And !$([MSBuild]::IsOSPlatform('osx')))" />
    </ItemGroup>
    <ItemGroup>
      <Folder Include="wwwroot\media" />
    </ItemGroup>
    <ItemGroup>
      <Compile Update="controllers\ContactController.cs">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Compile>
    </ItemGroup>
	<ItemGroup>
      <Content Update="wwwroot\statuscodes\500.html">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
    </ItemGroup>

    <PropertyGroup>
        <CopyRazorGenerateFilesToPublishDirectory>true</CopyRazorGenerateFilesToPublishDirectory>
    </PropertyGroup>

    <!-- Keep this as false if ModelsBuilder mode is InMemoryAuto -->
    <PropertyGroup>
        <RazorCompileOnBuild>false</RazorCompileOnBuild>
        <RazorCompileOnPublish>false</RazorCompileOnPublish>
    </PropertyGroup>

</Project>
