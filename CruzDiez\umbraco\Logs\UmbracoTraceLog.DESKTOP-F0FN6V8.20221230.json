{"@t":"2022-12-30T11:44:03.9562085Z","@mt":"Acquiring MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":14208,"ProcessName":"<PERSON><PERSON><PERSON><PERSON>","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2022-12-30T11:44:03.9714494Z","@mt":"Acquired MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":14208,"ProcessName":"CruzDiez","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2022-12-30T11:44:19.3055556Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)\r\n ---> System.ComponentModel.Win32Exception (53): Nie można odnaleźć ścieżki sieciowej.\r\n   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)\r\n   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)\r\n   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)\r\n   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)\r\n   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)\r\n   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)\r\n   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)\r\n   at System.Data.SqlClient.SqlConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)\r\nClientConnectionId:00000000-0000-0000-0000-000000000000\r\nError Number:53,State:0,Class:20","SourceContext":"object","ProcessId":14208,"ProcessName":"CruzDiez","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"WARN "}
{"@t":"2022-12-30T11:44:20.3259683Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)\r\n ---> System.ComponentModel.Win32Exception (53): Nie można odnaleźć ścieżki sieciowej.\r\n   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)\r\n   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)\r\n   at System.Data.SqlClient.SqlConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)\r\nClientConnectionId:00000000-0000-0000-0000-000000000000\r\nError Number:53,State:0,Class:20","SourceContext":"object","ProcessId":14208,"ProcessName":"CruzDiez","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"WARN "}
{"@t":"2022-12-30T11:44:21.3304543Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)\r\n ---> System.ComponentModel.Win32Exception (53): Nie można odnaleźć ścieżki sieciowej.\r\n   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)\r\n   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)\r\n   at System.Data.SqlClient.SqlConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)\r\nClientConnectionId:00000000-0000-0000-0000-000000000000\r\nError Number:53,State:0,Class:20","SourceContext":"object","ProcessId":14208,"ProcessName":"CruzDiez","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"WARN "}
{"@t":"2022-12-30T11:44:22.3412955Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)\r\n ---> System.ComponentModel.Win32Exception (53): Nie można odnaleźć ścieżki sieciowej.\r\n   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)\r\n   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)\r\n   at System.Data.SqlClient.SqlConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)\r\nClientConnectionId:00000000-0000-0000-0000-000000000000\r\nError Number:53,State:0,Class:20","SourceContext":"object","ProcessId":14208,"ProcessName":"CruzDiez","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"WARN "}
{"@t":"2022-12-30T11:44:23.3527782Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)\r\n ---> System.ComponentModel.Win32Exception (53): Nie można odnaleźć ścieżki sieciowej.\r\n   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)\r\n   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)\r\n   at System.Data.SqlClient.SqlConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)\r\nClientConnectionId:00000000-0000-0000-0000-000000000000\r\nError Number:53,State:0,Class:20","SourceContext":"object","ProcessId":14208,"ProcessName":"CruzDiez","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"WARN "}
{"@t":"2022-12-30T11:44:23.3543930Z","@mt":"Boot Failed","@l":"Error","@x":"Umbraco.Cms.Core.Exceptions.BootFailedException: A connection string is configured but Umbraco could not connect to the database.\r\n   at Umbraco.Cms.Infrastructure.Runtime.RuntimeState.DetermineRuntimeLevel()\r\n   at Umbraco.Cms.Infrastructure.Runtime.CoreRuntime.DetermineRuntimeLevel()","SourceContext":"Umbraco.Cms.Infrastructure.Runtime.CoreRuntime","ProcessId":14208,"ProcessName":"CruzDiez","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2022-12-30T11:44:23.5724318Z","@mt":"Now listening on: {address}","address":"https://localhost:44304","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":14208,"ProcessName":"CruzDiez","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2022-12-30T11:44:23.5726235Z","@mt":"Now listening on: {address}","address":"http://localhost:50765","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":14208,"ProcessName":"CruzDiez","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2022-12-30T11:44:23.5727962Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":14208,"ProcessName":"CruzDiez","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2022-12-30T11:44:23.5730201Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":14208,"ProcessName":"CruzDiez","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2022-12-30T11:44:23.5730457Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":14208,"ProcessName":"CruzDiez","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2022-12-30T11:44:31.6671745Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Umbraco.Cms.Core.Exceptions.BootFailedException: Boot failed: Umbraco cannot run. See Umbraco's log file for more details.\n\n-> Umbraco.Cms.Core.Exceptions.BootFailedException: A connection string is configured but Umbraco could not connect to the database.\n   at Umbraco.Cms.Infrastructure.Runtime.RuntimeState.DetermineRuntimeLevel()\r\n   at Umbraco.Cms.Infrastructure.Runtime.CoreRuntime.DetermineRuntimeLevel()\r\n   at Umbraco.Cms.Core.Exceptions.BootFailedException.Rethrow(BootFailedException bootFailedException)\r\n   at Umbraco.Cms.Web.Common.Middleware.BootFailedMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HMNABCP9RLIH:0000000F","RequestPath":"/","ConnectionId":"0HMNABCP9RLIH","ProcessId":14208,"ProcessName":"CruzDiez","ThreadId":18,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
