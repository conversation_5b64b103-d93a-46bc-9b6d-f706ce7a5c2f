@using Umbraco.Cms.Core.Routing
@using Umbraco.Extensions
@inherits Umbraco.Cms.Web.Common.Macros.PartialViewMacroPage
@inject IPublishedUrlProvider PublishedUrlProvider
@*
    This snippet makes a breadcrumb of parents using an unordered HTML list.

    How it works:
    - It uses the Ancestors() method to get all parents and then generates links so the visitor can go back
    - Finally it outputs the name of the current page (without a link)
*@

@{ var selection = Model?.Content.Ancestors().ToArray(); }

@if (selection?.Length > 0)
{
    <ul class="breadcrumb">
        @* For each page in the ancestors collection which have been ordered by Level (so we start with the highest top node first) *@
        @foreach (var item in selection.OrderBy(x => x.Level))
        {
            <li><a href="@item.Url(PublishedUrlProvider)">@item.Name</a> <span class="divider">/</span></li>
        }

        @* Display the current page as the last item in the list *@
        <li class="active">@Model?.Content.Name</li>
    </ul>
}
