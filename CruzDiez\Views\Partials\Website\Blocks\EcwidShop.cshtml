﻿@inherits UmbracoViewPage<SiteBuilderBlock>
@using USNSiteBuilder.Core.Models
@using USNSiteBuilder.Core.Extensions
@{
    //Available as Component Block only

    UsnglobalSettings globalSettings = (UsnglobalSettings)Model.BaseModel.GlobalSettings;

    if (globalSettings.EnableShop && globalSettings.Ecwid.HasValue())
    {
        var ecwid = globalSettings.Ecwid;

        <!-- Row - Component main -->
        <div class="row component-main">
            <div class="ecwidshop-listing col-12">
                <div id="my-store-@ecwid"></div>
                <div>
                    <script defer data-cfasync="false" type="text/javascript" src="https://app.ecwid.com/script.js?@(ecwid)&data_platform=code" charset="utf-8"></script>
                    <script defer type="text/javascript">
                        window.addEventListener('load', function () {
                            xProductBrowser("id=my-store-@(ecwid)");
                        })
                    </script>
                </div>
            </div>
        </div>
        <!--// Row - Component main -->
    }
}