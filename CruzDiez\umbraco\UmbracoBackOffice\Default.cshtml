﻿@using Microsoft.Extensions.Options;
@using System.Globalization
@using Umbraco.Cms.Core
@using Umbraco.Cms.Core.Configuration
@using Umbraco.Cms.Core.Configuration.Models
@using Umbraco.Cms.Core.Hosting
@using Umbraco.Cms.Core.Logging
@using Umbraco.Cms.Core.Routing
@using Umbraco.Cms.Core.Services
@using Umbraco.Cms.Core.WebAssets
@using Umbraco.Cms.Infrastructure.WebAssets
@using Umbraco.Cms.Web.BackOffice.Controllers
@using Umbraco.Cms.Web.BackOffice.Security
@using Umbraco.Extensions
@inject BackOfficeServerVariables backOfficeServerVariables
@inject IUmbracoVersion umbracoVersion
@inject IHostingEnvironment hostingEnvironment
@inject IOptions<GlobalSettings> globalSettings
@inject IRuntimeMinifier runtimeMinifier
@inject IProfilerHtml profilerHtml
@inject IIconService IconService
@inject IBackOfficeExternalLoginProviders externalLogins
@{
    bool.TryParse(Context.Request.Query["umbDebug"], out bool isDebug);
    var backOfficePath = globalSettings.Value.GetBackOfficePath(hostingEnvironment);
}

<!DOCTYPE html>

<html lang="@CultureInfo.CurrentCulture.Name">
<head>
    <base href="@backOfficePath.EnsureEndsWith('/')" />
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="robots" content="noindex, nofollow">
    <meta name="pinterest" content="nopin" />

    <title ng-bind="$root.locationTitle">Umbraco</title>

    @Html.Raw(await runtimeMinifier.RenderCssHereAsync(BackOfficeWebAssets.UmbracoInitCssBundleName))

</head>
<body ng-class="{'touch':touchDevice, 'emptySection':emptySection, 'umb-drawer-is-visible':drawer.show, 'umb-tour-is-visible': tour.show, 'tabbing-active':tabbingActive}" ng-controller="Umbraco.MainController" id="umbracoMainPageBody">

    <noscript>
        <div class="flex flex-wrap flex-column items-center justify-center" style="height: 100%">
            <h1 class="h3">
                <span style="width: 30px; height: 30px; vertical-align: text-bottom" class="flex-inline">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 315.89 315.89" fill="#3544b1">
                        <path d="M0,157.74A157.95,157.95,0,1,1,158,315.89,157.95,157.95,0,0,1,0,157.74Zm154.74,54.09a155.41,155.41,0,0,1-36.5-3.29,27.92,27.92,0,0,1-19.94-16q-5.35-12.34-5.21-38.1a243,243,0,0,1,1.69-26.84q1.55-13,3.09-21.46l1.07-5.59a2,2,0,0,0,0-.49,3.2,3.2,0,0,0-2.65-3.17L75.92,93.67h-.44a3.19,3.19,0,0,0-3.11,2.48c-.35,1.31-.56,2.27-1.17,5.38-1.16,6-2.24,11.85-3.43,20.38a264.17,264.17,0,0,0-2.3,27.94,145.24,145.24,0,0,0,0,19.57q.72,25.94,8.9,41.42t27.72,22.3q19.53,6.81,54.43,6.66h2.91q34.94.15,54.41-6.66t27.71-22.3q8.17-15.53,8.91-41.42a145.24,145.24,0,0,0,0-19.57,266.84,266.84,0,0,0-2.3-27.94c-1.2-8.44-2.27-14.26-3.44-20.38-.61-3.11-.81-4.07-1.16-5.38a3.21,3.21,0,0,0-3.12-2.48h-.52l-20.38,3.18a3.2,3.2,0,0,0-2.68,3.17,4,4,0,0,0,0,.49l1.08,5.59q1.55,8.48,3.12,21.46a245.68,245.68,0,0,1,1.65,26.84q.27,25.69-5.21,38.07a27.9,27.9,0,0,1-19.76,16.07,155.19,155.19,0,0,1-36.48,3.29Z" />
                    </svg>
                </span>
                Umbraco
            </h1>
            <p>For full functionality of Umbraco CMS it is necessary to enable JavaScript.</p>
            <p>Here are the <a href="https://www.enable-javascript.com/" target="_blank" rel="noopener" style="text-decoration: underline;">instructions how to enable JavaScript in your web browser</a>.</p>
        </div>
    </noscript>

    <div ng-hide="!authenticated" ng-cloak>

        <div style="display: none;" id="mainwrapper" class="clearfix">

            <umb-app-header></umb-app-header>

            <div class="umb-app-content">

                <umb-navigation></umb-navigation>

                <section id="contentwrapper">

                    <div id="contentcolumn">
                        <div class="umb-editor" ng-view></div>
                    </div>

                </section>

            </div>

        </div>

        <umb-tour ng-if="tour.show"
                  model="tour">
        </umb-tour>

        <!-- help dialog controller by the help button - this also forces the backoffice UI to shift 400px  -->
        <umb-drawer data-element="drawer" ng-if="drawer.show" model="drawer.model" view="drawer.view"></umb-drawer>

        <umb-search ng-if="search.show" on-close="closeSearch()"></umb-search>

    </div>

    <umb-notifications></umb-notifications>

    <umb-backdrop ng-if="backdrop.show || infiniteMode"
                  backdrop-opacity="backdrop.opacity"
                  highlight-element="backdrop.element"
                  highlight-prevent-click="backdrop.elementPreventClick"
                  disable-events-on-click="backdrop.disableEventsOnClick">
    </umb-backdrop>

    <umb-overlay ng-if="overlay.show"
                 model="overlay"
                 position="{{overlay.position}}"
                 size="overlay.size"
                 view="overlay.view"
                 name="overlay.name"
                 parent-scope="overlay.parentScope">
    </umb-overlay>

    <umb-editors ng-show="infiniteMode"></umb-editors>

    <umb-login ng-if="login.show"
               on-login="hideLoginScreen()">
    </umb-login>

    @await Html.BareMinimumServerVariablesScriptAsync(backOfficeServerVariables)

    <script>
        document.angularReady = function(app) {
            @await Html.AngularValueExternalLoginInfoScriptAsync(externalLogins, ViewData.GetExternalSignInProviderErrors())
            @Html.AngularValueResetPasswordCodeInfoScript(ViewData[ViewDataExtensions.TokenPasswordResetCode])
            @await Html.AngularValueTinyMceAssetsAsync(runtimeMinifier)
            //required for the noscript trick
            document.getElementById("mainwrapper").style.display = "inherit";
        }
    </script>

    <script src="@WebPath.Combine(backOfficePath.TrimStart("~"), "/lib/lazyload-js/LazyLoad.min.js")"></script>
    <script src="@Url.GetUrlWithCacheBust("Application", "BackOffice", null, hostingEnvironment, umbracoVersion, runtimeMinifier)"></script>

    @if (isDebug)
    {
        @Html.Raw(profilerHtml.Render())
    }

</body>
</html>
