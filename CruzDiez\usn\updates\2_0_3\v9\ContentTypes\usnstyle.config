﻿<?xml version="1.0" encoding="utf-8"?>
<ContentType Key="392be869-d517-4f99-a7e9-72b296346dd2" Alias="USNStyle" Level="2">
  <Info>
    <Name>Design</Name>
    <Icon>icon-brush color-deep-purple</Icon>
    <Thumbnail>folder.png</Thumbnail>
    <Description>Use this item to create a new "Design".</Description>
    <AllowAtRoot>False</AllowAtRoot>
    <IsListView>False</IsListView>
    <Variations>Nothing</Variations>
    <IsElement>false</IsElement>
    <HistoryCleanup>
      <PreventCleanup>False</PreventCleanup>
      <KeepAllVersionsNewerThanDays></KeepAllVersionsNewerThanDays>
      <KeepLatestVersionPerDayForDays></KeepLatestVersionPerDayForDays>
    </HistoryCleanup>
    <Folder>Website+Styles</Folder>
    <Compositions>
      <Composition Key="644e714a-e1d5-4457-a5bd-9c033e36cba7">USN_CMP_DisableDelete</Composition>
    </Compositions>
    <DefaultTemplate>USNHomeRedirect</DefaultTemplate>
    <AllowedTemplates>
      <Template Key="c6fe2eff-1c5e-42cf-90af-bce065a84310">USNHomeRedirect</Template>
    </AllowedTemplates>
  </Info>
  <Structure />
  <GenericProperties>
    <GenericProperty>
      <Key>77ea48b3-ed67-42a5-b80e-7469b9080777</Key>
      <Name>Additional button color CSS</Name>
      <Alias>additionalButtonColorCSS</Alias>
      <Definition>7d7e103e-3a2e-4ed9-8644-9e17656ffd76</Definition>
      <Type>USN.CodeEditor</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Use this field to create styles that should be included in the generated additional button colors. For example, instead of targeting specific colors with .c1-btn-bg, you can write .c{position}-btn-bg to target all repeatable button colors.]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="cSS">CSS</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>true</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>9e8462d7-79fc-40c0-87d2-84fd57ad7abe</Key>
      <Name>Additional content color CSS</Name>
      <Alias>additionalContentColorCSS</Alias>
      <Definition>7d7e103e-3a2e-4ed9-8644-9e17656ffd76</Definition>
      <Type>USN.CodeEditor</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Use this field to create styles that should be included in the generated additional content colors. For example, instead of targeting specific colors with .c1-bg, you can write .c{position}-bg to target all repeatable content colors.]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="cSS">CSS</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>true</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>409ae094-f85d-4ffb-bc0b-08cc50381334</Key>
      <Name>Body background image</Name>
      <Alias>bodyBackgroundImage</Alias>
      <Definition>135d60e0-64d9-49ed-ab08-893c9ba44ae5</Definition>
      <Type>Umbraco.MediaPicker</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[]]></Description>
      <SortOrder>0</SortOrder>
      <Tab Alias="backgrounds">Backgrounds</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>963462e3-b3a7-47e1-8d2a-cf314961054c</Key>
      <Name>Body background image options</Name>
      <Alias>bodyBGImageOptions</Alias>
      <Definition>f59624d3-16e9-41eb-8172-dd00e7b5b6e2</Definition>
      <Type>USN.BackgroundOptions</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="backgrounds">Backgrounds</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>d2fd6aa5-12e0-49fb-8d92-0f4f831ea591</Key>
      <Name>Custom CSS</Name>
      <Alias>customCSS</Alias>
      <Definition>7d5a8159-fd54-4c7a-93b0-1cb69949e273</Definition>
      <Type>USN.CodeEditor</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Add custom CSS that will only be applied to this design.]]></Description>
      <SortOrder>0</SortOrder>
      <Tab Alias="cSS">CSS</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>true</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>2ca13cf9-5a85-4d89-b5f9-3e0eaed94159</Key>
      <Name>Custom JS</Name>
      <Alias>customJS</Alias>
      <Definition>ad7bdcfc-33b3-467c-b9a4-b30f41b04249</Definition>
      <Type>USN.CodeEditor</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Add custom JavaScript that will only be applied to this design.]]></Description>
      <SortOrder>0</SortOrder>
      <Tab Alias="javaScript">JavaScript</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>true</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>72efda4b-e79c-4bff-876b-c5e7747905bd</Key>
      <Name>Favicon</Name>
      <Alias>faviconAndTouchIcons</Alias>
      <Definition>135d60e0-64d9-49ed-ab08-893c9ba44ae5</Definition>
      <Type>Umbraco.MediaPicker</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[A favicon is a small icon that serves as branding for your website. Appears on browser  tabs and favourites.]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="logo">Logo</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>4dcb5913-03e6-4dc1-b860-019cb47e3ef2</Key>
      <Name>Footer background image</Name>
      <Alias>footerBackgroundImage</Alias>
      <Definition>135d60e0-64d9-49ed-ab08-893c9ba44ae5</Definition>
      <Type>Umbraco.MediaPicker</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="backgrounds">Backgrounds</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>66616551-9b39-4532-84c8-db86dba3405a</Key>
      <Name>Footer background image options</Name>
      <Alias>footerBGImageOptions</Alias>
      <Definition>f59624d3-16e9-41eb-8172-dd00e7b5b6e2</Definition>
      <Type>USN.BackgroundOptions</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[]]></Description>
      <SortOrder>3</SortOrder>
      <Tab Alias="backgrounds">Backgrounds</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>2956a46c-4539-4356-aaf2-63a573c6fd50</Key>
      <Name>Frontend source</Name>
      <Alias>frontendSource</Alias>
      <Definition>4783b233-0649-4554-958b-0b68feac5e9f</Definition>
      <Type>USN.ThemePicker</Type>
      <Mandatory>true</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[This is the source folder name for your design. 

The default is uSkinned. 

If making changes to the source code you should create your own source folders for:
~/wwwroot/css/yoursource
~/wwwroot/images/yoursource
~/wwwroot/scripts/yoursource
~/Views/Partials/yoursource
~/EmailTemplates/yoursource

Register your own source folder in:
~/appsettings.json]]></Description>
      <SortOrder>0</SortOrder>
      <Tab Alias="advanced">Advanced</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>2944c427-dbd0-4358-9f56-5a6264cda499</Key>
      <Name>Style colors</Name>
      <Alias>styleColors</Alias>
      <Definition>5e74fadb-1b95-497c-8b68-979bddaeb0fc</Definition>
      <Type>USN.ThemeColors</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[]]></Description>
      <SortOrder>0</SortOrder>
      <Tab Alias="colors">Colors</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>26431901-7caf-415d-ad0f-95ab9af478f1</Key>
      <Name>Style fonts</Name>
      <Alias>styleFonts</Alias>
      <Definition>f35b6dcb-cb0f-4992-affe-d139d124c011</Definition>
      <Type>USN.ThemeFonts</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[]]></Description>
      <SortOrder>0</SortOrder>
      <Tab Alias="fonts">Fonts</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>82d76db4-78df-4a14-9b40-738d0716e878</Key>
      <Name>Style layout</Name>
      <Alias>styleLayout</Alias>
      <Definition>9ca75e2e-0c68-4629-adfc-a166e21727c8</Definition>
      <Type>USN.ThemeLayout</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[]]></Description>
      <SortOrder>0</SortOrder>
      <Tab Alias="layout">Layout</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>30d6e91a-6d34-4976-aa7c-77b41cbb394b</Key>
      <Name>Style spacing</Name>
      <Alias>styleSpacing</Alias>
      <Definition>ee4deab7-3254-45bc-a45b-9d9040b99046</Definition>
      <Type>USN.ThemeSpacing</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[]]></Description>
      <SortOrder>0</SortOrder>
      <Tab Alias="spacing">Spacing</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>51dc672d-18da-4532-ace5-085db5c6bfb4</Key>
      <Name>Website logo</Name>
      <Alias>websiteLogo</Alias>
      <Definition>135d60e0-64d9-49ed-ab08-893c9ba44ae5</Definition>
      <Type>Umbraco.MediaPicker</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[]]></Description>
      <SortOrder>0</SortOrder>
      <Tab Alias="logo">Logo</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
  </GenericProperties>
  <Tabs>
    <Tab>
      <Key>92a05cc3-366e-42a9-85be-b875f412f6fb</Key>
      <Caption>Layout</Caption>
      <Alias>layout</Alias>
      <Type>Tab</Type>
      <SortOrder>0</SortOrder>
    </Tab>
    <Tab>
      <Key>9dc76472-7acd-4ad0-be20-ce91764a7210</Key>
      <Caption>Logo</Caption>
      <Alias>logo</Alias>
      <Type>Tab</Type>
      <SortOrder>1</SortOrder>
    </Tab>
    <Tab>
      <Key>d848facd-e5ed-4180-9281-c78ff8d3e008</Key>
      <Caption>Colors</Caption>
      <Alias>colors</Alias>
      <Type>Tab</Type>
      <SortOrder>2</SortOrder>
    </Tab>
    <Tab>
      <Key>19b27e13-d8f1-445d-8798-82b8286131cd</Key>
      <Caption>Spacing</Caption>
      <Alias>spacing</Alias>
      <Type>Tab</Type>
      <SortOrder>3</SortOrder>
    </Tab>
    <Tab>
      <Key>41d9de95-662b-4a1b-bc13-a1acf7035b92</Key>
      <Caption>Fonts</Caption>
      <Alias>fonts</Alias>
      <Type>Tab</Type>
      <SortOrder>4</SortOrder>
    </Tab>
    <Tab>
      <Key>9862dd02-5384-40bb-83cb-a9558a98aab4</Key>
      <Caption>Backgrounds</Caption>
      <Alias>backgrounds</Alias>
      <Type>Tab</Type>
      <SortOrder>5</SortOrder>
    </Tab>
    <Tab>
      <Key>49959cb3-cf6a-46e3-b0cd-8fae19c764bd</Key>
      <Caption>CSS</Caption>
      <Alias>cSS</Alias>
      <Type>Tab</Type>
      <SortOrder>6</SortOrder>
    </Tab>
    <Tab>
      <Key>d41e691f-281e-4d66-8400-0a47a1a18d80</Key>
      <Caption>JavaScript</Caption>
      <Alias>javaScript</Alias>
      <Type>Tab</Type>
      <SortOrder>7</SortOrder>
    </Tab>
    <Tab>
      <Key>b9d4b803-d1b5-4397-aa27-02ab57993b5c</Key>
      <Caption>Advanced</Caption>
      <Alias>advanced</Alias>
      <Type>Tab</Type>
      <SortOrder>999</SortOrder>
    </Tab>
  </Tabs>
</ContentType>