@using System.Web
@using Microsoft.AspNetCore.Html
@using Newtonsoft.Json.Linq
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage<dynamic>

@if (Model is JObject && Model?.sections is not null)
{
  var oneColumn = ((System.Collections.ICollection)Model.sections).Count == 1;

    <div class="umb-grid">
        @if (oneColumn)
        {
            foreach (var section in Model.sections)
            {
                <div class="grid-section">
                    @foreach (var row in section.rows)
                    {
                        renderRow(row, true);
                    }
                </div>
            }
        }
        else
        {
            <div class="container">
                <div class="row clearfix">
                    @foreach (var sec in Model.sections)
                    {
                        <div class="grid-section">
                            <div class="<EMAIL> column">
                                @foreach (var row in sec.rows)
                                {
                                    renderRow(row, false);
                                }
                            </div>
                        </div>
                    }
                </div>
            </div>
        }
    </div>
}

@functions{

    private async Task renderRow(dynamic row, bool singleColumn)
    {
        <div @RenderElementAttributes(row)>
            @if (singleColumn) {
                @:<div class="container">
            }
            <div class="row clearfix">
                @foreach (var area in row.areas)
                {
                    <div class="<EMAIL> column">
                        <div @RenderElementAttributes(area)>
                            @foreach (var control in area.controls)
                            {
                                if (control?.editor?.view != null)
                                {
                                    <text>@await Html.PartialAsync("grid/editors/base", (object)control)</text>
                                }
                            }
                        </div>
                    </div>
                 }
            </div>
            @if (singleColumn) {
                @:</div>
            }
        </div>
    }

}

@functions{

    public static HtmlString RenderElementAttributes(dynamic contentItem)
    {
        var attrs = new List<string>();
        JObject cfg = contentItem.config;

        if (cfg != null)
        {
            foreach (JProperty property in cfg.Properties())
            {
                var propertyValue = HttpUtility.HtmlAttributeEncode(property.Value.ToString());
                attrs.Add(property.Name + "=\"" + propertyValue + "\"");
            }
        }

        JObject style = contentItem.styles;

        if (style != null)
        {
            var cssVals = new List<string>();
            foreach (JProperty property in style.Properties())
            {
                var propertyValue = property.Value.ToString();
                if (string.IsNullOrWhiteSpace(propertyValue) == false)
                {
                    cssVals.Add(property.Name + ":" + propertyValue + ";");
                }
            }

            if (cssVals.Any())
                attrs.Add("style=\"" + HttpUtility.HtmlAttributeEncode(string.Join(" ", cssVals)) + "\"");
        }

        return new HtmlString(string.Join(" ", attrs));
    }
}
