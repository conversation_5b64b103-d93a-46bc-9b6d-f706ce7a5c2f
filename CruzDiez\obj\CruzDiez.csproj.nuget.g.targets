﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.aspnetcore.mvc.razor.runtimecompilation\6.0.5\buildTransitive\net6.0\Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.aspnetcore.mvc.razor.runtimecompilation\6.0.5\buildTransitive\net6.0\Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.targets')" />
    <Import Project="$(NuGetPackageRoot)uskinnedsitebuilder\3.0.5\buildTransitive\uSkinnedSiteBuilder.targets" Condition="Exists('$(NuGetPackageRoot)uskinnedsitebuilder\3.0.5\buildTransitive\uSkinnedSiteBuilder.targets')" />
    <Import Project="$(NuGetPackageRoot)umbraco.cms\10.4.0\buildTransitive\Umbraco.Cms.targets" Condition="Exists('$(NuGetPackageRoot)umbraco.cms\10.4.0\buildTransitive\Umbraco.Cms.targets')" />
  </ImportGroup>
</Project>