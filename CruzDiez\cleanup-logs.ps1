# Umbraco Log Cleanup Script
# This script helps manage Umbraco log files to prevent excessive storage usage

param(
    [int]$DaysToKeep = 7,
    [switch]$WhatIf = $false
)

$LogPath = "umbraco\Logs"
$CurrentDate = Get-Date

Write-Host "Umbraco Log Cleanup Script" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green
Write-Host ""

if (-not (Test-Path $LogPath)) {
    Write-Host "Log directory not found: $LogPath" -ForegroundColor Red
    exit 1
}

# Get all log files
$LogFiles = Get-ChildItem -Path $LogPath -Filter "*.json" | Sort-Object LastWriteTime

Write-Host "Found $($LogFiles.Count) log files in $LogPath" -ForegroundColor Yellow
Write-Host ""

# Calculate total size
$TotalSize = ($LogFiles | Measure-Object -Property Length -Sum).Sum
$TotalSizeMB = [math]::Round($TotalSize / 1MB, 2)

Write-Host "Current total log size: $TotalSizeMB MB" -ForegroundColor Yellow
Write-Host ""

# Find files older than specified days
$CutoffDate = $CurrentDate.AddDays(-$DaysToKeep)
$OldFiles = $LogFiles | Where-Object { $_.LastWriteTime -lt $CutoffDate }

if ($OldFiles.Count -eq 0) {
    Write-Host "No log files older than $DaysToKeep days found." -ForegroundColor Green
    exit 0
}

Write-Host "Files older than $DaysToKeep days:" -ForegroundColor Red
$OldFilesSize = 0
foreach ($file in $OldFiles) {
    $fileSizeMB = [math]::Round($file.Length / 1MB, 2)
    $OldFilesSize += $file.Length
    Write-Host "  $($file.Name) - $fileSizeMB MB - $($file.LastWriteTime)" -ForegroundColor Red
}

$OldFilesSizeMB = [math]::Round($OldFilesSize / 1MB, 2)
Write-Host ""
Write-Host "Total size of old files: $OldFilesSizeMB MB" -ForegroundColor Red
Write-Host ""

if ($WhatIf) {
    Write-Host "WhatIf mode: Would delete $($OldFiles.Count) files totaling $OldFilesSizeMB MB" -ForegroundColor Yellow
} else {
    $confirmation = Read-Host "Delete these $($OldFiles.Count) files? (y/N)"
    if ($confirmation -eq 'y' -or $confirmation -eq 'Y') {
        foreach ($file in $OldFiles) {
            Remove-Item $file.FullName -Force
            Write-Host "Deleted: $($file.Name)" -ForegroundColor Green
        }
        Write-Host ""
        Write-Host "Cleanup completed. Freed $OldFilesSizeMB MB of space." -ForegroundColor Green
    } else {
        Write-Host "Cleanup cancelled." -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "Usage examples:" -ForegroundColor Cyan
Write-Host "  .\cleanup-logs.ps1                    # Delete files older than 7 days"
Write-Host "  .\cleanup-logs.ps1 -DaysToKeep 14     # Delete files older than 14 days"
Write-Host "  .\cleanup-logs.ps1 -WhatIf            # Preview what would be deleted"
