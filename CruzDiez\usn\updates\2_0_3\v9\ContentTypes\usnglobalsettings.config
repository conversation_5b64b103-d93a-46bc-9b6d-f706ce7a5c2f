﻿<?xml version="1.0" encoding="utf-8"?>
<ContentType Key="b4419f5c-5e9f-4112-8834-d00c7362cecb" Alias="USNGlobalSettings" Level="2">
  <Info>
    <Name>Settings</Name>
    <Icon>icon-settings color-orange</Icon>
    <Thumbnail>folder.png</Thumbnail>
    <Description>Manage the global settings that relate to your site.</Description>
    <AllowAtRoot>False</AllowAtRoot>
    <IsListView>False</IsListView>
    <Variations>Culture</Variations>
    <IsElement>false</IsElement>
    <HistoryCleanup>
      <PreventCleanup>False</PreventCleanup>
      <KeepAllVersionsNewerThanDays></KeepAllVersionsNewerThanDays>
      <KeepLatestVersionPerDayForDays></KeepLatestVersionPerDayForDays>
    </HistoryCleanup>
    <Folder>Global</Folder>
    <Compositions>
      <Composition Key="644e714a-e1d5-4457-a5bd-9c033e36cba7">USN_CMP_DisableDelete</Composition>
    </Compositions>
    <DefaultTemplate>USNHomeRedirect</DefaultTemplate>
    <AllowedTemplates>
      <Template Key="c6fe2eff-1c5e-42cf-90af-bce065a84310">USNHomeRedirect</Template>
    </AllowedTemplates>
  </Info>
  <Structure />
  <GenericProperties>
    <GenericProperty>
      <Key>26121282-7ba1-46e0-b241-0be0404d48cb</Key>
      <Name>Accept notification button text</Name>
      <Alias>acceptNotificationButtonText</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[]]></Description>
      <SortOrder>4</SortOrder>
      <Tab Alias="notification">Notification</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>127ddbf3-2de8-49fa-beed-58e4726e3f97</Key>
      <Name>After opening body scripts</Name>
      <Alias>afterOpeningBodyScripts</Alias>
      <Definition>4e9f23cc-3cde-4d44-873b-e710aae97947</Definition>
      <Type>USN.CodeEditor</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Anything you enter here will be placed after the opening &lt;body&gt; tag on every page of your website.]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="scripts">Scripts</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>true</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>d472dc25-fd6a-425a-8caa-10c52a775612</Key>
      <Name>Before closing body scripts</Name>
      <Alias>beforeClosingBodyScripts</Alias>
      <Definition>4e9f23cc-3cde-4d44-873b-e710aae97947</Definition>
      <Type>USN.CodeEditor</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Anything you enter here will be placed before the closing &lt;/body&gt; tag on every page of your website.]]></Description>
      <SortOrder>3</SortOrder>
      <Tab Alias="scripts">Scripts</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>true</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>dbe56e80-7cc5-4fe0-b986-255bf928f2d0</Key>
      <Name>Bottom components</Name>
      <Alias>defaultBottomComponents</Alias>
      <Definition>09b761e4-10a2-498b-bc3e-d7755c9ee8ad</Definition>
      <Type>Umbraco.BlockList</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="globalContent">Global Content</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>85dd1224-f793-486f-83da-39a2a8431f9c</Key>
      <Name>Email marketing platform default subscriber list ID</Name>
      <Alias>defaultNewsletterSubscriberListID</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Newsletter Signup will only be available if a Subscriber List ID is available. Individual signup forms can have their own Subscriber List ID however the one entered here will be used as the default.]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="form">Form</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>18e715fe-0a15-4810-8287-b139966374e3</Key>
      <Name>Default social share image</Name>
      <Alias>defaultOpenGraphImage</Alias>
      <Definition>135d60e0-64d9-49ed-ab08-893c9ba44ae5</Definition>
      <Type>Umbraco.MediaPicker</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[The default image used when someone shares a page on social media, such as, Twitter or Facebook.

We suggest that you use an image of at least 1200x630 pixels.]]></Description>
      <SortOrder>3</SortOrder>
      <Tab Alias="general">General</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>f6611d9d-0bed-4ba2-a3d7-c1a98150980d</Key>
      <Name>Pods</Name>
      <Alias>defaultPods</Alias>
      <Definition>2d49d5ac-dfe8-4a6e-8dcf-7b0cb6a3a36f</Definition>
      <Type>Umbraco.BlockList</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[]]></Description>
      <SortOrder>3</SortOrder>
      <Tab Alias="globalContent">Global Content</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>5a55cad5-05fa-4715-85c2-4b71892f6210</Key>
      <Name>Top components</Name>
      <Alias>defaultTopComponents</Alias>
      <Definition>09b761e4-10a2-498b-bc3e-d7755c9ee8ad</Definition>
      <Type>Umbraco.BlockList</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="globalContent">Global Content</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>9b37cdb3-5620-48e6-b35b-d1e7fbd15596</Key>
      <Name>Enter your shop id</Name>
      <Alias>ecwid</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="shop">Shop</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>ddc239fe-f972-47cf-920a-2283a7f2d1a8</Key>
      <Name>Email marketing platform</Name>
      <Alias>emailMarketingPlatform</Alias>
      <Definition>4e29a3b2-7891-44de-acda-18bebd9b1003</Definition>
      <Type>USN.OptionsShowHide</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[If nothing is selected Newsletter Signup will not be available.]]></Description>
      <SortOrder>0</SortOrder>
      <Tab Alias="form">Form</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>4bc8e126-25e9-4f09-a158-aedfe837ee76</Key>
      <Name>Enable</Name>
      <Alias>enableNotification</Alias>
      <Definition>8c352287-9c5d-4b53-8f65-3fde5e3029e7</Definition>
      <Type>USN.TrueFalseShowHide</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[]]></Description>
      <SortOrder>0</SortOrder>
      <Tab Alias="notification">Notification</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>b91d94f3-91ef-49b5-bdc4-7866557918de</Key>
      <Name>Enable shop</Name>
      <Alias>enableShop</Alias>
      <Definition>f8d0629f-788b-499d-8014-4276759ede8f</Definition>
      <Type>USN.TrueFalseShowHide</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="shop">Shop</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>36e7f6f1-17bc-4931-b511-279a58458aa5</Key>
      <Name>Facebook pixel</Name>
      <Alias>facebookPixel</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[<a href="https://www.facebook.com/business/help/651294705016616" target="_blank">Visit Facebook</a>]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="analytics">Analytics</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>8399430b-dc8f-4eb9-9345-1bbb9b91cbe8</Key>
      <Name>Instructions</Name>
      <Alias>globalContentInstructions</Alias>
      <Definition>b7f65b8c-35a0-4a22-a6e3-26357f748fed</Definition>
      <Type>USN.Label</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[<div class="alert  alert-usn" role="alert">Content added here will appear on every page of your website unless you choose to override or hide it via the 'Extra Content' tab on each page.</div>]]></Description>
      <SortOrder>0</SortOrder>
      <Tab Alias="globalContent">Global Content</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>08e7e512-fa17-4189-8581-3018970d83ef</Key>
      <Name>Hide period</Name>
      <Alias>globalHidePeriod</Alias>
      <Definition>2e6d3631-066e-44b8-aec4-96f09099b2b5</Definition>
      <Type>Umbraco.Integer</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Enter the number of days before a user is shown the modal window again. If nothing is entered this will default to 365.]]></Description>
      <SortOrder>7</SortOrder>
      <Tab Alias="globalContent">Global Content</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>4e9ecdf8-36ed-4c68-9503-a94920b2bbe2</Key>
      <Name>Modal window</Name>
      <Alias>globalModalWindow</Alias>
      <Definition>410b5f04-72ab-4ac0-aa27-6c410e349ddc</Definition>
      <Type>USN.OptionsShowHide</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[]]></Description>
      <SortOrder>4</SortOrder>
      <Tab Alias="globalContent">Global Content</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>4e4437b8-54b7-430e-b9ca-305224516c0b</Key>
      <Name>Open after</Name>
      <Alias>globalOpenAfter</Alias>
      <Definition>68082b22-0652-4e6a-8795-418215e8bf60</Definition>
      <Type>Umbraco.Slider</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Modal window will open after user has been on page for selected seconds.]]></Description>
      <SortOrder>6</SortOrder>
      <Tab Alias="globalContent">Global Content</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>87d0ce52-5883-434f-b674-cdf1a20f409c</Key>
      <Name>Scroll percentage</Name>
      <Alias>globalScrollPercentage</Alias>
      <Definition>72d3cb72-c5e0-435a-8e26-41b3a010a6e3</Definition>
      <Type>Umbraco.Slider</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Modal window will open after user scrolls percentage of page height.]]></Description>
      <SortOrder>5</SortOrder>
      <Tab Alias="globalContent">Global Content</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>c53bf935-2fab-47a3-84c8-27f2573172b5</Key>
      <Name>Modal content</Name>
      <Alias>globalSelectReusableContent</Alias>
      <Definition>c911326c-983a-487d-b0c9-b61115ac07d8</Definition>
      <Type>USN.MultiNodeTreePicker</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[From the picker, navigate to either reusable folders to select your content.]]></Description>
      <SortOrder>8</SortOrder>
      <Tab Alias="globalContent">Global Content</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>9ca7dcd7-9f22-4f09-bca7-481edb6eb5a4</Key>
      <Name>Content direction</Name>
      <Alias>globalTextDirection</Alias>
      <Definition>14cddaa4-2ef5-4bf1-b6f6-bea13f3972de</Definition>
      <Type>USN.OptionsShowHide</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[]]></Description>
      <SortOrder>6</SortOrder>
      <Tab Alias="general">General</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>4bb0c540-a75c-46e8-a519-709b1a9a6d4f</Key>
      <Name>Google analytics tracking ID</Name>
      <Alias>googleAnalyticsTrackingID</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[<a href="https://analytics.google.com/" target="_blank">Visit Google Analytics</a>]]></Description>
      <SortOrder>0</SortOrder>
      <Tab Alias="analytics">Analytics</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>d4d73730-b8e5-47f5-9f4a-8faf38ea3dc2</Key>
      <Name>Google reCAPTCHA V2 secret key</Name>
      <Alias>googleReCAPTCHASecretKey</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[]]></Description>
      <SortOrder>5</SortOrder>
      <Tab Alias="form">Form</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>1ee04f0b-f2c1-4cc9-b494-4160e4880f16</Key>
      <Name>Google reCAPTCHA V2 site key</Name>
      <Alias>googleReCAPTCHASiteKey</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Google reCAPTCHA V2 will be displayed on Contact Forms and Newsletter Signup Forms if a site key and secret key have been entered here. Select the checkbox option when creating your keys.

<a href="https://www.google.com/recaptcha/" target="_blank">https://www.google.com/recaptcha/</a>]]></Description>
      <SortOrder>4</SortOrder>
      <Tab Alias="form">Form</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>64e414d0-e063-451c-8b61-f74ba3c7f1e7</Key>
      <Name>Google reCAPTCHA V3 score</Name>
      <Alias>googleReCAPTCHAV3Score</Alias>
      <Definition>97b4a616-0957-456d-bd88-8ff89540ea40</Definition>
      <Type>Umbraco.Slider</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Decide the minimum confidence score you will accept as a valid form submission. Reduce the score if you are seeing an increase in spam.]]></Description>
      <SortOrder>8</SortOrder>
      <Tab Alias="form">Form</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>8c99fe59-f0fb-4b51-ac95-9d40cd882c3d</Key>
      <Name>Google reCAPTCHA V3 secret key</Name>
      <Alias>googleReCAPTCHAV3SecretKey</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[]]></Description>
      <SortOrder>7</SortOrder>
      <Tab Alias="form">Form</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>1e352d5f-86ae-4eea-829b-78bf82707c1b</Key>
      <Name>Google reCAPTCHA V3 site key</Name>
      <Alias>googleReCAPTCHAV3SiteKey</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Google reCAPTCHA V3 will be active on Contact Forms and Newsletter Signup Forms if a site key and secret key have been entered here.

<a href="https://www.google.com/recaptcha/" target="_blank">https://www.google.com/recaptcha/</a>]]></Description>
      <SortOrder>6</SortOrder>
      <Tab Alias="form">Form</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>73e991f3-5985-47b5-a6a0-17c7bb4a3ee7</Key>
      <Name>Google reCAPTCHA version</Name>
      <Alias>googleReCAPTCHAVersion</Alias>
      <Definition>4da2c14b-d290-450b-bb20-44866a5ed693</Definition>
      <Type>USN.OptionsShowHide</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Note: If reCAPTCHA is enabled, saving these settings will overwrite the values held in ~/App_Plugins/UmbracoForms/UmbracoForms.config]]></Description>
      <SortOrder>3</SortOrder>
      <Tab Alias="form">Form</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>29e7d457-3ae2-401d-8f01-c57a42e52854</Key>
      <Name>Google tag manager ID</Name>
      <Alias>googleTagManagerID</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[<a href="https://tagmanager.google.com/" target="_blank">Visit Google Tag Manager</a>]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="analytics">Analytics</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>95553b85-56b9-47bf-92eb-450fb5be136f</Key>
      <Name>Header opening scripts</Name>
      <Alias>headerOpeningScripts</Alias>
      <Definition>4e9f23cc-3cde-4d44-873b-e710aae97947</Definition>
      <Type>USN.CodeEditor</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Anything you enter here will be placed after the opening &lt;head&gt; tag on every page of your website.]]></Description>
      <SortOrder>0</SortOrder>
      <Tab Alias="scripts">Scripts</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>true</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>cbd5b572-ccc3-489b-aaca-32202b11dc6a</Key>
      <Name>Header closing scripts</Name>
      <Alias>headerScripts</Alias>
      <Definition>4e9f23cc-3cde-4d44-873b-e710aae97947</Definition>
      <Type>USN.CodeEditor</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Anything you enter here will be placed before the closing &lt;/head&gt; tag on every page of your website.]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="scripts">Scripts</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>true</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>3f7f0e3d-4ac3-424f-842c-db5e5fbeb416</Key>
      <Name>Instructions</Name>
      <Alias>instructions</Alias>
      <Definition>b7f65b8c-35a0-4a22-a6e3-26357f748fed</Definition>
      <Type>USN.Label</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[<div class="alert  alert-usn" role="alert">This section controls global settings that relate to your site as a whole.</div>]]></Description>
      <SortOrder>0</SortOrder>
      <Tab Alias="general">General</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>9fc1bfa6-4282-402a-bc92-72977fce4f8c</Key>
      <Name>Email marketing platform API key</Name>
      <Alias>newsletterAPIKey</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[If nothing is entered Newsletter Signup will not be available.]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="form">Form</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>af52f546-7e8f-4026-a7fa-632152e16f10</Key>
      <Name>Heading</Name>
      <Alias>notificationHeading</Alias>
      <Definition>bacaab0e-355d-44cd-839e-b99bd0317e83</Definition>
      <Type>USN.Heading</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="notification">Notification</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>d92adb0b-5a5f-4306-b9aa-cd0ff9505d2e</Key>
      <Name>Secondary heading</Name>
      <Alias>notificationSecondaryHeading</Alias>
      <Definition>bacaab0e-355d-44cd-839e-b99bd0317e83</Definition>
      <Type>USN.Heading</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="notification">Notification</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>ddb00e5b-3cd4-4e44-870b-4872731fd5c5</Key>
      <Name>Text</Name>
      <Alias>notificationText</Alias>
      <Definition>74bce676-3dec-49e2-9b44-3e4baedf7c54</Definition>
      <Type>Umbraco.TinyMCE</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[]]></Description>
      <SortOrder>3</SortOrder>
      <Tab Alias="notification">Notification</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>70c36809-1430-446d-80a8-bde8822fff82</Key>
      <Name>Override language code reference</Name>
      <Alias>overrideLanguageCodeReference</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[<a target="_blank" href="https://www.w3docs.com/learn-html/html-language-codes.html">ISO Language Codes</a>]]></Description>
      <SortOrder>5</SortOrder>
      <Tab Alias="general">General</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>71fb8d9c-8e9a-4f4f-8ba8-af5e6091c88d</Key>
      <Name>Page not found</Name>
      <Alias>pageNotFound</Alias>
      <Definition>fd1e0da5-5606-4862-b679-5d0cf3a52a59</Definition>
      <Type>Umbraco.ContentPicker</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Page displayed if the requested URL is not found.

A domain must be assigned to your Home node via Culture and hostnames for this to function.]]></Description>
      <SortOrder>8</SortOrder>
      <Tab Alias="general">General</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>5392bc1e-28b2-4e03-bb42-c11de29b1762</Key>
      <Name>Remember acceptance duration (days)</Name>
      <Alias>rememberAcceptanceDurationDays</Alias>
      <Definition>2e6d3631-066e-44b8-aec4-96f09099b2b5</Definition>
      <Type>Umbraco.Integer</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[If nothing is entered this will default to 365 days.]]></Description>
      <SortOrder>5</SortOrder>
      <Tab Alias="notification">Notification</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>074dd43b-c990-4a38-9ff4-499866a7f5ab</Key>
      <Name>Search results page</Name>
      <Alias>searchResultsPage</Alias>
      <Definition>fd1e0da5-5606-4862-b679-5d0cf3a52a59</Definition>
      <Type>Umbraco.ContentPicker</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[If no page is selected the search form will not appear.]]></Description>
      <SortOrder>7</SortOrder>
      <Tab Alias="general">General</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>0684e6b3-f14c-4651-878a-cb9efe8b6132</Key>
      <Name>Instructions</Name>
      <Alias>shopInstructions</Alias>
      <Definition>b7f65b8c-35a0-4a22-a6e3-26357f748fed</Definition>
      <Type>USN.Label</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[<div class="alert  alert-usn" role="alert"><p class="heading">Shop with Ecwid</p><p>With our <a href="http://open.ecwid.com/39sfFq" target="_blank">Ecwid eCommerce</a> integration you can start selling online for free in minutes.</p><p class="link"><a class="btn btn-primary" href="http://open.ecwid.com/39sfFq" target="_blank">Sign up and start selling online for free</a></p><p>Once you've signed up, enter your shop ID and select your shop page below. Then add the Ecwid component to your shop page.</p></div>]]></Description>
      <SortOrder>0</SortOrder>
      <Tab Alias="shop">Shop</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>9a70f5f7-59b8-47ec-a275-20dcdcda067c</Key>
      <Name>Select your shop page</Name>
      <Alias>shopPage</Alias>
      <Definition>fd1e0da5-5606-4862-b679-5d0cf3a52a59</Definition>
      <Type>Umbraco.ContentPicker</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[This is the page where you have added your Ecwid shop component.]]></Description>
      <SortOrder>3</SortOrder>
      <Tab Alias="shop">Shop</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>be34899e-c6c3-4450-807e-48630497412d</Key>
      <Name>Twitter pixel</Name>
      <Alias>twitterPixel</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[<a href="https://business.twitter.com/en/help/campaign-measurement-and-analytics/conversion-tracking-for-websites.html" target="_blank">Visit Twitter</a>]]></Description>
      <SortOrder>3</SortOrder>
      <Tab Alias="analytics">Analytics</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>c2534f9e-a954-4a32-bad4-1125f787680b</Key>
      <Name>Twitter site username</Name>
      <Alias>twitterSiteUsername</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation>^(?!@.*$).*</Validation>
      <Description><![CDATA[Username for the website used in the twitter card footer.]]></Description>
      <SortOrder>4</SortOrder>
      <Tab Alias="general">General</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage>Enter username only without '@'</ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>e424a151-9e10-4cf5-9857-ea3519d08785</Key>
      <Name>Website name</Name>
      <Alias>websiteName</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>true</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[The text entered here will be be added to the default Meta title of your webpages and used for the logo text (hidden if a website logo has been added to your design).]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="general">General</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>7e103c3f-6a1c-4d08-8fb9-8ae88fb851a5</Key>
      <Name>Design style</Name>
      <Alias>websiteStyle</Alias>
      <Definition>b9385249-7e56-4ee4-9851-d350b9bdba72</Definition>
      <Type>Umbraco.MultiNodeTreePicker</Type>
      <Mandatory>true</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Select the 'Design style' to be used for your website.]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="general">General</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
  </GenericProperties>
  <Tabs>
    <Tab>
      <Key>48c80031-0544-436f-87ff-91f9bfdab302</Key>
      <Caption>General</Caption>
      <Alias>general</Alias>
      <Type>Tab</Type>
      <SortOrder>0</SortOrder>
    </Tab>
    <Tab>
      <Key>cd7d8097-2fa4-497f-95a3-b1165a726ecc</Key>
      <Caption>Analytics</Caption>
      <Alias>analytics</Alias>
      <Type>Tab</Type>
      <SortOrder>1</SortOrder>
    </Tab>
    <Tab>
      <Key>8b96c63e-04c1-4202-96f7-c032d713d7f9</Key>
      <Caption>Form</Caption>
      <Alias>form</Alias>
      <Type>Tab</Type>
      <SortOrder>2</SortOrder>
    </Tab>
    <Tab>
      <Key>326c3df0-8b25-4a61-b407-e8e7cfc7e4e7</Key>
      <Caption>Shop</Caption>
      <Alias>shop</Alias>
      <Type>Tab</Type>
      <SortOrder>3</SortOrder>
    </Tab>
    <Tab>
      <Key>43b68713-2a2c-418d-8382-9c21f65d35f8</Key>
      <Caption>Global Content</Caption>
      <Alias>globalContent</Alias>
      <Type>Tab</Type>
      <SortOrder>4</SortOrder>
    </Tab>
    <Tab>
      <Key>ec449b17-6f59-48f9-9e6f-f3c8c31445a4</Key>
      <Caption>Notification</Caption>
      <Alias>notification</Alias>
      <Type>Tab</Type>
      <SortOrder>5</SortOrder>
    </Tab>
    <Tab>
      <Key>743f513b-bdfb-4975-bb3c-ed76a6d2da9b</Key>
      <Caption>Scripts</Caption>
      <Alias>scripts</Alias>
      <Type>Tab</Type>
      <SortOrder>6</SortOrder>
    </Tab>
  </Tabs>
</ContentType>