{"@t":"2023-01-17T16:08:02.2601713Z","@mt":"Acquiring MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":19404,"ProcessName":"iisexpress","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-01-17T16:08:02.2768984Z","@mt":"Acquired MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":19404,"ProcessName":"iisexpress","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-01-17T16:08:17.6506443Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)\r\n ---> System.ComponentModel.Win32Exception (53): Nie można odnaleźć ścieżki sieciowej.\r\n   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)\r\n   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)\r\n   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)\r\n   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)\r\n   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)\r\n   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)\r\n   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)\r\n   at System.Data.SqlClient.SqlConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)\r\nClientConnectionId:00000000-0000-0000-0000-000000000000\r\nError Number:53,State:0,Class:20","SourceContext":"object","ProcessId":19404,"ProcessName":"iisexpress","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"WARN "}
{"@t":"2023-01-17T16:08:18.6575627Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)\r\n ---> System.ComponentModel.Win32Exception (53): Nie można odnaleźć ścieżki sieciowej.\r\n   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)\r\n   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)\r\n   at System.Data.SqlClient.SqlConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)\r\nClientConnectionId:00000000-0000-0000-0000-000000000000\r\nError Number:53,State:0,Class:20","SourceContext":"object","ProcessId":19404,"ProcessName":"iisexpress","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"WARN "}
{"@t":"2023-01-17T16:08:19.6646803Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)\r\n ---> System.ComponentModel.Win32Exception (53): Nie można odnaleźć ścieżki sieciowej.\r\n   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)\r\n   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)\r\n   at System.Data.SqlClient.SqlConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)\r\nClientConnectionId:00000000-0000-0000-0000-000000000000\r\nError Number:53,State:0,Class:20","SourceContext":"object","ProcessId":19404,"ProcessName":"iisexpress","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"WARN "}
{"@t":"2023-01-17T16:08:20.6792670Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)\r\n ---> System.ComponentModel.Win32Exception (53): Nie można odnaleźć ścieżki sieciowej.\r\n   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)\r\n   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)\r\n   at System.Data.SqlClient.SqlConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)\r\nClientConnectionId:00000000-0000-0000-0000-000000000000\r\nError Number:53,State:0,Class:20","SourceContext":"object","ProcessId":19404,"ProcessName":"iisexpress","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"WARN "}
{"@t":"2023-01-17T16:08:21.6864968Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)\r\n ---> System.ComponentModel.Win32Exception (53): Nie można odnaleźć ścieżki sieciowej.\r\n   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)\r\n   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)\r\n   at System.Data.SqlClient.SqlConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)\r\nClientConnectionId:00000000-0000-0000-0000-000000000000\r\nError Number:53,State:0,Class:20","SourceContext":"object","ProcessId":19404,"ProcessName":"iisexpress","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"WARN "}
{"@t":"2023-01-17T16:08:21.6877329Z","@mt":"Boot Failed","@l":"Error","@x":"Umbraco.Cms.Core.Exceptions.BootFailedException: A connection string is configured but Umbraco could not connect to the database.\r\n   at Umbraco.Cms.Infrastructure.Runtime.RuntimeState.DetermineRuntimeLevel()\r\n   at Umbraco.Cms.Infrastructure.Runtime.CoreRuntime.DetermineRuntimeLevel()","SourceContext":"Umbraco.Cms.Infrastructure.Runtime.CoreRuntime","ProcessId":19404,"ProcessName":"iisexpress","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-01-17T16:08:21.9153482Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":19404,"ProcessName":"iisexpress","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-01-17T16:08:21.9157579Z","@mt":"Hosting environment: {envName}","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":19404,"ProcessName":"iisexpress","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-01-17T16:08:21.9159816Z","@mt":"Content root path: {contentRoot}","contentRoot":"C:\\Users\\<USER>\\Desktop\\Programowanie\\A-Soft\\cruz\\CruzDiez","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":19404,"ProcessName":"iisexpress","ThreadId":1,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"INFO "}
{"@t":"2023-01-17T16:08:22.0649647Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Umbraco.Cms.Core.Exceptions.BootFailedException: Boot failed: Umbraco cannot run. See Umbraco's log file for more details.\n\n-> Umbraco.Cms.Core.Exceptions.BootFailedException: A connection string is configured but Umbraco could not connect to the database.\n   at Umbraco.Cms.Infrastructure.Runtime.RuntimeState.DetermineRuntimeLevel()\r\n   at Umbraco.Cms.Infrastructure.Runtime.CoreRuntime.DetermineRuntimeLevel()\r\n   at Umbraco.Cms.Core.Exceptions.BootFailedException.Rethrow(BootFailedException bootFailedException)\r\n   at Umbraco.Cms.Web.Common.Middleware.BootFailedMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.<>c__DisplayClass6_1.<<UseMiddlewareInterface>b__1>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"80000002-000b-ff00-b63f-84710c7967bb","RequestPath":"/","ProcessId":19404,"ProcessName":"iisexpress","ThreadId":5,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-01-17T16:09:36.9971110Z","@mt":"Failed to detected SqlServer version.","@l":"Error","@x":"System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)\r\n ---> System.ComponentModel.Win32Exception (53): Nie można odnaleźć ścieżki sieciowej.\r\n   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)\r\n   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)\r\n   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)\r\n   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)\r\n   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)\r\n   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)\r\n   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)\r\n   at System.Data.SqlClient.SqlConnection.Open()\r\n   at Umbraco.Cms.Infrastructure.Persistence.SqlSyntax.SqlServerSyntaxProvider.GetSetVersion(String connectionString, String providerName, ILogger logger)\r\nClientConnectionId:00000000-0000-0000-0000-000000000000\r\nError Number:53,State:0,Class:20","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.UmbracoDatabaseFactory","ProcessId":19404,"ProcessName":"iisexpress","ThreadId":37,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
{"@t":"2023-01-17T16:09:37.0881466Z","@mt":"Unhandled exception in recurring hosted service.","@l":"Error","@x":"System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)\r\n ---> System.ComponentModel.Win32Exception (53): Nie można odnaleźć ścieżki sieciowej.\r\n   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)\r\n   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)\r\n   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)\r\n   at System.Data.SqlClient.SqlConnection.Open()\r\n   at NPoco.Database.OpenSharedConnectionImp(Boolean isInternal)\r\n   at NPoco.Database.BeginTransaction(IsolationLevel isolationLevel)\r\n   at Umbraco.Cms.Core.Scoping.Scope.get_Database()\r\n   at Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.KeyValueRepository.PerformGet(String id)\r\n   at Umbraco.Cms.Core.Services.Implement.KeyValueService.GetValue(String key)\r\n   at Umbraco.Cms.Core.Services.MetricsConsentService.GetConsentLevel()\r\n   at Umbraco.Cms.Core.Telemetry.TelemetryService.GetVersion()\r\n   at Umbraco.Cms.Core.Telemetry.TelemetryService.TryGetTelemetryReportData(TelemetryReportData& telemetryReportData)\r\n   at Umbraco.Cms.Infrastructure.HostedServices.ReportSiteTask.PerformExecuteAsync(Object state)\r\n   at Umbraco.Cms.Infrastructure.HostedServices.RecurringHostedServiceBase.ExecuteAsync(Object state)\r\nClientConnectionId:00000000-0000-0000-0000-000000000000\r\nError Number:53,State:0,Class:20","SourceContext":"Umbraco.Cms.Infrastructure.HostedServices.ReportSiteTask","ProcessId":19404,"ProcessName":"iisexpress","ThreadId":37,"AppDomainId":1,"AppDomainAppId":"11eae537823fec7cf76c732f79e0fa7168dcea1d","MachineName":"DESKTOP-F0FN6V8","Log4NetLevel":"ERROR"}
