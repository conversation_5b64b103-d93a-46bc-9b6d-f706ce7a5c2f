@inherits Umbraco.Cms.Web.Common.Macros.PartialViewMacroPage
@using Microsoft.AspNetCore.Http.Extensions
@using Umbraco.Cms.Web.Common.Models
@using Umbraco.Cms.Web.Website.Controllers
@using Umbraco.Extensions

@{
    var isLoggedIn = Context.User?.Identity?.IsAuthenticated ?? false;
    var logoutModel = new PostRedirectModel();
    // You can modify this to redirect to a different URL instead of the current one
    logoutModel.RedirectUrl = null;
}

@if (isLoggedIn)
{
    <div class="login-status">

        <p>Welcome back <strong>@Context?.User?.Identity?.Name</strong>!</p>

        @using (Html.BeginUmbracoForm<UmbLoginStatusController>("HandleLogout", new { RedirectUrl = logoutModel.RedirectUrl }))
        {
            <button type="submit" class="btn btn-primary">Log out</button>
        }

    </div>
}
